<!--automobile/pages/evolution/popup/index.wxml-->
<view class="container_hd {{visible ? 'show' : 'disappear'}}" style="height: 100vh;" capture-bind:tap="isShowOpen" data-touch="true">
  <view class='z-height'>
    <view class="sort-list">
      <view class="sort-list__title">
        <view>年份选择</view>
      </view>
      <view class="sort-list__content">
        <block wx:for="{{ timeData }}" wx:key="index" data-item="{{item}}">
          <view class="content-item" bindtap="handleYearChange" data-item="{{ item }}">
            <view class=" {{ checkData.value === item.value ? 'active' : ''}}"> {{ item.title }}</view>
            <view wx:if="{{ checkData.value === item.value }}" class="check"></view>
          </view>
        </block>
      </view>
    </view>
  </view>
</view>