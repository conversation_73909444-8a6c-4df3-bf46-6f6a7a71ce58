
import {  orgRelationEnt } from "../../../../../service/financing";
const app = getApp()

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    orgId: {
      type: String,
      observer(val) {
        this.setData({ org_id: val }, () => {
          this.bazaarRefresher();
        })
      }
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    org_id:'',
    // 请求相关
    bazaarParms: {
      page_index: 1, //偏移量
      page_size: 10, //每页多少条
    },
    bazaarlist: [], //获取列表
    bazaarIsFlag: true, //节流 true允许
    bazaarHasData: true, //  是否还有数据
    bazaarIsTriggered: false, // 下拉刷新状态
    count: 0, //数量
  },

  /**
   * 组件的方法列表
   */
  methods: {
    //获取列表
  initGetList(callback) {
    const that = this;
    let { bazaarlist,bazaarHasData, bazaarParms, org_id} = that.data;
    that.setData({
      bazaarIsFlag: false,
      bazaarHasData: true
    });
    orgRelationEnt({...bazaarParms, org_id}).then(res => {
      let {datalist, count} = res
      let ary = datalist;
      if (bazaarlist.length > count) bazaarHasData = false;
      that.setData({
        bazaarlist: bazaarlist.concat(ary),
        bazaarHasData,
        bazaarIsFlag: true,
        count
      }, () => { 
        // const { bazaarlist} = this.data;
        // console.log('bazaarlist.length >= count', bazaarlist.length > count);
        // if (!that.data.bazaarlist.length) that.setData({
        //   bazaarHasData: bazaarlist.length > count
        // });
      })
      callback && callback()
    }).catch(err => {
      callback && callback()
      app.showToast('获取数据失败!请稍后再试')
      that.setData({
        bazaarlist: [],
        bazaarHasData: true,
        count: 0
      });
      console.log(err)
    })
  },
  // 下拉刷新
  bazaarRefresher() {
    const that = this;
    wx.showNavigationBarLoading()
    let {  bazaarParms, type } = that.data
    let obj = {
      ...bazaarParms,
      page_index: 1,
      page_size: 10
    }
    that.setData({
      bazaarParms: obj,
      bazaarlist: [],
      bazaarHasData: true,
    }, () => that.initGetList(() => {
      that.setData({
        bazaarIsTriggered: false
      })
      wx.hideNavigationBarLoading()
    }))
  },
  //加载更多
  bazaarloadMore() {
    let {
      bazaarParms,
      bazaarHasData,
      bazaarIsFlag
    } = this.data;
    if (!bazaarHasData) return;
    if (!bazaarIsFlag) return; //节流
    bazaarParms.page_index += 1;
    this.setData({
      bazaarParms
    }, () => this.initGetList());
  },
  //  跳转企业详情
  goToEntDetail(e){
    let { item } = e.currentTarget.dataset;
    if(item.ent_id) {
      const url = encodeURIComponent(`https://reporth5.handidit.com?entId=${item.ent_id}`)
      this.triggerEvent("close");
      app.route(this, `/subPackage/pages/webs/index?url=${url}`) 
    }
  },
  }
})
