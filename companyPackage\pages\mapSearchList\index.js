import {home, chain} from '../../../service/api';
import {getHeight} from '../../../utils/height';
import {debounce} from '../../../utils/formate';
const app = getApp();
// 最近搜索看后端出不出接口
Page({
  data: {
    isLogin: false,
    inputShowed: true,
    // 搜索相关
    ent_name: '', //搜索值
    historyList: [],
    chainList: [], // 搜索列表
    scrollHeight: 'auto', // 列表滚动高度
    isLogin: app.isLogin()
  },
  onShow() {
    wx.removeStorageSync('mapSearchItem');
    const {login} = app.globalData;
    // this.scrollH()
    this.setData(
      {
        isLogin: login
      },
      () => {
        login && app.showLoading('加载中');
        login &&
          Promise.all([this.getSearchHisList()]).finally(res => {
            wx.hideLoading();
          });
      }
    );
  },
  onUnload() {
    wx.removeStorageSync('industrySearchHistory');
  },
  // input相关--onblur后面真机看是否保留
  onConfirm: function (e) {
    if (this.data.isHeightParams) return;
    let keyword = e.detail.value;
    const {isLogin} = this.data;
    if (keyword) {
      isLogin && this.addHistorySearch(keyword);
    }
  },
  addHistorySearch(value) {
    let historySearch = wx.getStorageSync('industrySearchHistory') || [];
    let has = historySearch.includes(value);
    if (has) {
      let index = historySearch.findIndex(item => item == value);
      if (index == 0) return;
      historySearch.splice(index, 1);
    }
    let len = historySearch.length;
    if (len >= 10) {
      historySearch.pop();
    }
    historySearch.unshift(value);
    wx.setStorageSync('industrySearchHistory', historySearch);
    this.setData({
      historyList: historySearch
    });
  },
  handleIcon(e) {
    const type = e.currentTarget.dataset['index'];
    const that = this;
    switch (type) {
      // 情况最近搜索
      case 'a':
        wx.showModal({
          title: '删除搜索',
          content: '确定要删除最近搜索?',
          success: function (res) {
            if (res.confirm) {
              wx.removeStorageSync('industrySearchHistory');
              that.setData({
                showVisible: false,
                historyList: []
              });
              // 发请求同步
              // home.clearHistory('ENTERPRISE');
            }
          }
        });
        break;
      default:
        break;
    }
  },
  onClear() {
    this.unLocked();
    this.setData({
      ent_name: ''
    });
    this.scrollH();
    this.inputQuest();
  },
  init() {
    this.setData({
      ent_name: '',
      inputShowed: false
    });
  },
  isBlur() {
    this.setData({
      inputShowed: true
    });
  },
  onBlur() {
    // 拿到ent_name --传入最近搜索历史
    const ent_nameue = this.data.ent_name;
    if (ent_nameue.trim().length > 0) {
      if (this.data.isHeightParams) return;
      this.addHistorySearch(ent_nameue);
    } else {
      this.setData({
        inputShowed: false
      });
    }
  },
  onInput: debounce(function ([...e]) {
    const {isLogin} = this.data;
    console.log('isLogin', isLogin);
    let keyword = e[0].detail.value;
    if (keyword || keyword == '') {
      this.setData({
        ent_name: keyword
      });
      if (keyword && isLogin) {
        this.addHistorySearch(keyword);
      }
      this.inputQuest();
    }
  }),
  goBack() {
    this.unLocked();
    this.init();
    app.route(this, null, 'navigateBack');
  },
  unLocked() {
    wx.hideKeyboard();
    this.setData({
      inputShowed: false
    });
  },
  searchRegion(keyword) {
    const key = 'EUPBZ-6MD3K-I6OJB-ALA4P-DZGD3-54F67';
    const url = `https://apis.map.qq.com/ws/place/v1/suggestion?keyword=${encodeURIComponent(
      keyword
    )}&key=${key}&page_index=1&page_size=20`;
    return new Promise((resolve, reject) => {
      wx.request({
        url,
        success: res => {
          if (res.data.status === 0) {
            const pois = res.data.data;
            // console.log('222', pois);

            // 提取你需要的信息：名称 + 经纬度
            const results = pois.map(poi => ({
              address: poi.address,
              title: poi.title,
              location: poi.location || {}
            }));

            resolve(results);
          } else {
            reject(res.data.message);
          }
        },
        fail: err => {
          reject(err);
        }
      });
    });
  },

  async inputQuest() {
    const {ent_name: keyword} = this.data;
    if (!keyword) return;
    const list = await this.searchRegion(keyword);
    console.log('222', list);
    const chainList = list.filter(item => {
      return item.title.indexOf(keyword) != -1;
    });

    // 发请求地图接口 请求地区（经纬度）
    this.setData({
      chainList: this.onHandlerKewWord(chainList, 'title')
    });
  },
  // 关键词处理
  onHandlerKewWord(list, key) {
    const {ent_name} = this.data;
    return list.reduce((result, items) => {
      const _tagName = items[key].replace(
        new RegExp(`(${ent_name})`),
        '*=*$1*=*'
      );
      const split = _tagName.split('*=*');
      const handler = split.map(word => {
        if (word === ent_name) return {text: word, type: 'HIGH'};
        return {text: word, type: 'DEFAULT'};
      });
      return [...result, {...items, label: handler}];
    }, []);
  },
  // 点击产业链
  onIndustrClick({
    currentTarget: {
      dataset: {item}
    }
  }) {
    const {title, location} = item;
    // 本地存点击的经纬度 然后路由返回
    wx.setStorageSync('mapSearchItem', JSON.stringify({title, location}));
    app.route(this, null, 'navigateBack');
  },
  // 点击最近搜索
  historyTap(e) {
    const keyword = e.target.dataset['item'];
    this.setData(
      {
        inputShowed: true,
        ent_name: keyword
      },
      () => this.inputQuest()
    );
  },

  async getSearchHisList() {
    //获取搜索历史 列表
    let arr = [];
    home
      .getHistory('CHAIN')
      .then(res => {
        if (res && res.length > 0) {
          arr = res.map(item => item.key_word);
          arr = [...new Set(arr)];
        }
        wx.setStorageSync('industrySearchHistory', arr);
        this.setData(
          {
            historyList: arr
          },
          () => {
            this.scrollH();
          }
        );
      })
      .catch(err => {
        console.log(err);
      });
  },

  scrollH() {
    getHeight(this, ['.searchs', '.search_a', '.his_titles'], data => {
      const {screeHeight, res} = data;
      const h1 = res[0]?.height || 0;
      const h2 = res[1]?.height || 0;
      const h3 = res[2]?.height || 0;
      const scrollHeight = screeHeight - h1 - h2 - h3 - 30;
      const searchScrollHeight = screeHeight - h1 - 30;
      this.setData({
        scrollHeight,
        searchScrollHeight
      });
    });
  },
  // 去详情页面
  goDetail({
    currentTarget: {
      dataset: {item}
    }
  }) {
    const {enterprise_id: chain_code, enterprise_name: chain_name} = item;
    const url = `/companyPackage/pages/industryChain/chainList/chainList?chainName=${chain_name}&chain_code=${chain_code}`;
    app.route(this, url);
  }
});
