import {getHeight} from '../../../utils/height';
import {debounce} from '../../../utils/formate';
const app = getApp();

Page({
  data: {
    inputShowed: true,
    // 搜索相关
    ent_name: '', //搜索值
    historyList: [],
    chainList: [], // 搜索列表
    searchScrollHeight: 'auto', // 搜索列表滚动高度
    isLogin: app.isLogin(),
    // 高德地图API配置
    AMAP_KEY: 'd9a2bead78cad70c5e01899e3f5d5381',
    HISTORY_STORAGE_KEY: 'mapSearchHistory' // 本地存储key
  },
  onShow() {
    wx.removeStorageSync('mapSearchItem');
    const {login} = app.globalData;

    this.setData({
      isLogin: login
    });

    // 加载本地搜索历史
    if (login) {
      this.loadLocalHistory();
    }

    this.scrollH();
  },

  onUnload() {
    // 清理相关缓存
    wx.removeStorageSync('mapSearchItem');
  },
  /**
   * 搜索确认事件
   */
  onConfirm: function (e) {
    const keyword = e.detail.value?.trim();
    if (keyword && this.data.isLogin) {
      this.addHistorySearch(keyword);
    }
  },

  /**
   * 添加搜索历史到本地存储
   * @param {string} value 搜索关键词
   */
  addHistorySearch(value) {
    if (!value?.trim()) return;

    const {HISTORY_STORAGE_KEY} = this.data;
    let historyList = wx.getStorageSync(HISTORY_STORAGE_KEY) || [];

    // 如果已存在，先移除
    const existIndex = historyList.findIndex(item => item === value);
    if (existIndex !== -1) {
      if (existIndex === 0) return; // 已经是第一个，无需操作
      historyList.splice(existIndex, 1);
    }

    // 添加到开头
    historyList.unshift(value);

    // 限制最多保存10条
    if (historyList.length > 10) {
      historyList = historyList.slice(0, 10);
    }

    // 保存到本地存储
    wx.setStorageSync(HISTORY_STORAGE_KEY, historyList);

    // 更新页面数据
    this.setData({
      historyList
    });
  },

  /**
   * 加载本地搜索历史
   */
  loadLocalHistory() {
    const {HISTORY_STORAGE_KEY} = this.data;
    const historyList = wx.getStorageSync(HISTORY_STORAGE_KEY) || [];
    this.setData({
      historyList
    });
  },
  /**
   * 处理图标点击事件（清除历史记录）
   */
  handleIcon(e) {
    const type = e.currentTarget.dataset['index'];

    if (type === 'a') {
      wx.showModal({
        title: '删除搜索',
        content: '确定要删除最近搜索?',
        success: res => {
          if (res.confirm) {
            this.clearSearchHistory();
          }
        }
      });
    }
  },

  /**
   * 清除搜索历史
   */
  clearSearchHistory() {
    const {HISTORY_STORAGE_KEY} = this.data;
    wx.removeStorageSync(HISTORY_STORAGE_KEY);
    this.setData({
      historyList: []
    });
  },
  onClear() {
    this.unLocked();
    this.setData({
      ent_name: ''
    });
    this.scrollH();
    this.inputQuest();
  },
  init() {
    this.setData({
      ent_name: '',
      inputShowed: false
    });
  },
  isBlur() {
    this.setData({
      inputShowed: true
    });
  },
  /**
   * 输入框失焦事件
   */
  onBlur() {
    const entName = this.data.ent_name?.trim();
    if (entName && this.data.isLogin) {
      this.addHistorySearch(entName);
    } else if (!entName) {
      this.setData({
        inputShowed: false
      });
    }
  },
  onInput: debounce(function ([...e]) {
    const {isLogin} = this.data;
    console.log('isLogin', isLogin);
    let keyword = e[0].detail.value;
    if (keyword || keyword == '') {
      this.setData({
        ent_name: keyword
      });
      if (keyword && isLogin) {
        this.addHistorySearch(keyword);
      }
      this.inputQuest();
    }
  }),
  goBack() {
    this.unLocked();
    this.init();
    app.route(this, null, 'navigateBack');
  },
  unLocked() {
    wx.hideKeyboard();
    this.setData({
      inputShowed: false
    });
  },
  /**
   * 使用高德地图API搜索地点
   * @param {string} keyword 搜索关键词
   * @returns {Promise} 搜索结果
   */
  searchRegion(keyword) {
    const {AMAP_KEY} = this.data;

    return new Promise((resolve, reject) => {
      wx.request({
        url: 'https://restapi.amap.com/v3/place/text',
        data: {
          key: AMAP_KEY,
          keywords: keyword,
          types: '', // 可以指定类型，如：餐饮服务|商务住宅|生活服务
          city: '', // 可以指定城市
          children: 1,
          offset: 20, // 每页记录数，最大50
          page: 1,
          extensions: 'base' // 返回结果详细程度
        },
        timeout: 5000,
        success: res => {
          if (res.data && res.data.status === '1') {
            const pois = res.data.pois || [];

            // 转换为统一格式
            const results = pois.map(poi => ({
              address: poi.address || poi.pname + poi.cityname + poi.adname,
              title: poi.name,
              location: {
                lat: parseFloat(poi.location.split(',')[1]), // 高德返回格式：经度,纬度
                lng: parseFloat(poi.location.split(',')[0])
              }
            }));

            resolve(results);
          } else {
            console.error('高德地图搜索失败:', res.data?.info || '未知错误');
            reject(res.data?.info || '搜索失败');
          }
        },
        fail: err => {
          console.error('高德地图搜索请求失败:', err);
          reject(err);
        }
      });
    });
  },

  /**
   * 执行搜索请求
   */
  async inputQuest() {
    const {ent_name: keyword} = this.data;
    if (!keyword?.trim()) {
      this.setData({
        chainList: []
      });
      return;
    }

    try {
      const list = await this.searchRegion(keyword);

      // 处理搜索结果，添加高亮
      const chainList = this.onHandlerKewWord(list, 'title');

      this.setData({
        chainList
      });
    } catch (error) {
      console.error('搜索失败:', error);
      this.setData({
        chainList: []
      });

      wx.showToast({
        title: '搜索失败，请重试',
        icon: 'none'
      });
    }
  },
  /**
   * 关键词高亮处理
   * @param {Array} list 搜索结果列表
   * @param {string} key 需要高亮的字段名
   * @returns {Array} 处理后的列表
   */
  onHandlerKewWord(list, key) {
    const {ent_name} = this.data;
    if (!ent_name) return list;

    return list.map(item => {
      const text = item[key] || '';
      const regex = new RegExp(`(${ent_name})`, 'gi');
      const parts = text.split(regex);

      const label = parts.map(part => ({
        text: part,
        type: regex.test(part) ? 'HIGH' : 'DEFAULT'
      }));

      return {
        ...item,
        label
      };
    });
  },

  /**
   * 点击搜索结果项
   */
  onIndustrClick({
    currentTarget: {
      dataset: {item}
    }
  }) {
    const {title, location} = item;

    // 保存选中的地点信息到本地存储
    wx.setStorageSync('mapSearchItem', JSON.stringify({title, location}));

    // 返回上一页
    app.route(this, null, 'navigateBack');
  },
  /**
   * 点击历史搜索项
   */
  historyTap(e) {
    const keyword = e.target.dataset['item'];
    if (keyword) {
      this.setData(
        {
          inputShowed: true,
          ent_name: keyword
        },
        () => {
          this.inputQuest();
        }
      );
    }
  },

  /**
   * 计算滚动区域高度
   */
  scrollH() {
    getHeight(this, ['.searchs', '.search_a'], data => {
      const {screeHeight, res} = data;
      const h1 = res[0]?.height || 0;
      const h2 = res[1]?.height || 0;
      const searchScrollHeight = screeHeight - h1 - h2 - 30;

      this.setData({
        searchScrollHeight
      });
    });
  }
});
