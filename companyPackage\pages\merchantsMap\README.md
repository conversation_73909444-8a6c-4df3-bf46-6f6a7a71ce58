# MapMixin 使用说明

## 概述

`mapMixin.js` 是一个优化后的微信小程序地图混入（Behavior），提供了地图操作的基础功能，包括定位、拖动、逆向地理编码等。现已更新为使用高德地图API。

## 主要优化

### 1. 节流优化
- 使用节流机制防止频繁调用逆向解析接口
- 位置更新节流时间：1.5秒
- 地图拖动节流时间：800ms
- 避免在一定范围内连续拖动时频繁调用接口

### 2. 代码结构优化
- 模块化设计，功能分离
- 完善的错误处理和参数验证
- 详细的 JSDoc 注释
- 资源清理机制

### 3. 预留接口调用口子
- 提供 `setMapDragEndCallback` 方法设置拖动结束回调
- 每次拖动后会调用预设的回调函数
- 方便后续补充业务逻辑

## 使用方法

### 1. 在页面中引入 Behavior

```javascript
const mapMixin = require('./mapMixin.js');

Page({
  behaviors: [mapMixin()],

  data: {
    key: 'd9a2bead78cad70c5e01899e3f5d5381' // 高德地图API密钥（已内置）
  },

  onLoad() {
    // 设置地图拖动结束后的回调
    this.setMapDragEndCallback((latitude, longitude) => {
      console.log('地图拖动结束:', latitude, longitude);
      // 在这里添加你的业务逻辑
      // 例如：调用获取附近商户的接口
      this.getNearbyMerchants(latitude, longitude);
    });
  },

  // 示例：获取附近商户
  getNearbyMerchants(latitude, longitude) {
    // 这里是你要补充的接口调用
    wx.request({
      url: 'YOUR_API_ENDPOINT',
      data: {
        lat: latitude,
        lng: longitude,
        radius: 1000 // 搜索半径
      },
      success: (res) => {
        // 处理返回的商户数据
        console.log('附近商户:', res.data);
      }
    });
  }
});
```

### 2. 在 WXML 中使用

```xml
<map
  id="map"
  latitude="{{latitude}}"
  longitude="{{longitude}}"
  markers="{{markers}}"
  bindregionchange="regionchange"
  show-location="{{true}}"
  style="width: 100%; height: 100vh;">
</map>

<!-- 回到当前位置按钮 -->
<button bindtap="backToCurrentLocation">回到当前位置</button>

<!-- 显示当前地址 -->
<view wx:if="{{locationName}}">
  当前位置：{{locationName}}
</view>

<!-- 错误信息显示 -->
<view wx:if="{{error}}" class="error">
  {{error}}
</view>
```

## API 说明

### 数据字段

| 字段名       | 类型   | 说明             |
| ------------ | ------ | ---------------- |
| latitude     | Number | 当前纬度         |
| longitude    | Number | 当前经度         |
| markers      | Array  | 地图标记点数组   |
| locationName | String | 当前位置地址名称 |
| mapCtx       | Object | 地图上下文对象   |
| error        | String | 错误信息         |

### 方法

#### regionchange(e)
地图区域变化事件处理函数，会自动进行节流处理。

#### backToCurrentLocation()
回到当前位置。

#### setMapCenter(latitude, longitude)
手动设置地图中心点。

#### setMapDragEndCallback(callback)
设置地图拖动结束后的回调函数。

**参数：**
- `callback` (Function): 回调函数，接收 `(latitude, longitude)` 参数

**示例：**
```javascript
this.setMapDragEndCallback((lat, lng) => {
  console.log('新的中心点:', lat, lng);
  // 调用你的业务接口
});
```

#### getLocationName(longitude, latitude, callback)
逆向地理编码，将经纬度转换为地址名称。

**参数：**
- `longitude` (Number): 经度
- `latitude` (Number): 纬度
- `callback` (Function): 回调函数，接收地址字符串参数

## 注意事项

1. **API Key**: 高德地图API key已内置在代码中，无需额外配置
2. **权限**: 确保小程序已获取定位权限
3. **节流**: 拖动地图时会自动进行节流，避免频繁调用接口
4. **错误处理**: 所有网络请求都有错误处理，失败时会在控制台输出错误信息
5. **资源清理**: 页面销毁时会自动清理定位监听和定时器
6. **地图类型**: 现已切换为高德地图API，逆向地理编码使用高德地图服务

## 错误处理

mixin 内置了完善的错误处理机制：

- 参数验证
- 网络请求超时（5秒）
- API key 检查
- mapCtx 初始化检查
- 定位服务启动失败处理

错误信息会通过 `error` 字段反馈给页面，可以在界面上显示给用户。
