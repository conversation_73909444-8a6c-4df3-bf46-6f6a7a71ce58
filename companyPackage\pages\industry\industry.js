// companyPackage/pages/industry.js
Page({

    /**
     * 页面的初始数据
     */
    data: {
        activeIndex: 0,
        tabs: [{
                title: '权威榜单',
                code: 'expedient'
            },
            {
                title: '行业榜单',
                code: 'industry'
            },
            {
                title: '概念榜单',
                code: 'concept'
            }
        ],
        tagActiveIdx: 0,
        tag: [{
                name: '全部',
                code: ''
            },
            {
                name: '生物医药',
                code: ''
            },
            {
                name: '装备制造',
                code: ''
            },
            {
                name: '人工智能',
                code: ''
            },
            {
                name: '新能源汽车',
                code: ''
            }
        ],
        scrollHeight: 'auto',
        statusHeight: 'auto'
    },

    /**
     * 生命周期函数--监听页面加载
     */
    onLoad() {

    },
    onReady: function (options) {
        this.scrollH()
    },
    // 每次点击就发请求 ，第一次进入页面获取一次数据 
    onTabClick: function (e) {
        this.setData({
            activeIndex: e.currentTarget.id
        });
    },
    onTagClick: function (e) {

        this.setData({
            tagActiveIdx: e.currentTarget.id
        });
    },
    // 动态获取页面高度 
    scrollH() {
        var that = this;
        wx.getSystemInfo({
            success: (res) => {
                let statusHeight = wx.getSystemInfoSync().statusBarHeight
                // 这里是个bug如果不减 scroll就会超出这部分
                let screeHeight = wx.getSystemInfoSync().windowHeight - statusHeight
                //   通过query获取其余盒子的高度 
                let query = wx.createSelectorQuery().in(that)
                query.select('.industyr-nav').boundingClientRect()
                query.select('.industry-input').boundingClientRect()
                query.select('.industy-tag').boundingClientRect()
                // 通过query.exec返回数组
                query.exec(res => {
                    let h1 = res[0].height
                    let h2 = res[1].height
                    let h3 = res[2].height
                    console.log(screeHeight, h1, h2, h3)
                    let scrollHeight = screeHeight - h1 - h2 - h3
                    that.setData({
                        scrollHeight: scrollHeight,
                        statusHeight: statusHeight
                    })
                })
            },
        })
    },
})