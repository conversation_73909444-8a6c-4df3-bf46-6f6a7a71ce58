// companyPackage/pages/authoritativeEnter/index.js
Page({
  data: {
    keyword: '',
    // mock数据
    rankTypes: [{
        id: 1,
        name: '综合榜',
        lists: [{
            id: 101,
            title: '企业榜',
            count: 1567
          },
          {
            id: 102,
            title: '人物榜',
            count: 982
          },
          {
            id: 103,
            title: '产品榜',
            count: 2345
          },
          {
            id: 104,
            title: '创新榜',
            count: 876
          },
          {
            id: 105,
            title: '科技榜',
            count: 1234
          }
        ]
      },
      {
        id: 2,
        name: '行业榜',
        lists: [{
            id: 201,
            title: '互联网榜',
            count: 1245
          },
          {
            id: 202,
            title: '金融榜',
            count: 867
          },
          {
            id: 203,
            title: '医疗榜',
            count: 543
          },
          {
            id: 204,
            title: '教育榜',
            count: 789
          },
          {
            id: 205,
            title: '制造业榜',
            count: 1023
          }
        ]
      }
    ],
    // 过滤后的数据
    filteredRankTypes: []
  },

  onLoad() {
    // 初始化时将所有数据设置到filteredRankTypes
    this.setData({
      filteredRankTypes: this.data.rankTypes
    });
  },
  // /companyPackage/pages/authoritativeList/authoritativeList, 权威榜单列表

  onShow() {},

  // 处理输入框输入
  handleInput(e) {
    let keyword = e.detail;
    if (keyword || keyword === '') {
      this.setData({
        keyword: keyword
      });

      // 过滤数据
      this.filterRankData(keyword);
    }
  },

  // 过滤并高亮匹配的数据
  filterRankData(keyword) {
    if (!keyword) {
      // 如果关键词为空，显示所有数据且不高亮
      this.setData({
        filteredRankTypes: this.data.rankTypes
      });
      return;
    }

    // 复制原始数据进行处理
    const filteredData = this.data.rankTypes
      .map(type => {
        // 过滤并高亮匹配的榜单
        const matchedLists = type.lists.map(list => {
          // 检查榜单标题是否匹配
          const highlightedTitle = this.highlightKeyword(list.title, keyword);

          // 返回带有高亮标题的榜单
          return {
            ...list,
            highlightedTitle: highlightedTitle,
            matched: list.title.includes(keyword)
          };
        });
        const matched = matchedLists.some(i => i.matched)
        // 返回处理后的榜单类型
        return {
          ...type,
          lists: matchedLists.filter(i => i.matched),
          matched
        };
      }).filter(i => i.matched)
    this.setData({
      filteredRankTypes: filteredData.length > 0 ? filteredData : []
    });
  },

  // 高亮关键词
  highlightKeyword(text, keyword) {
    if (!keyword || !text.includes(keyword)) {
      return text;
    }

    // 将文本分割成匹配和非匹配部分
    const parts = [];
    let lastIndex = 0;
    let index = text.indexOf(keyword);

    while (index !== -1) {
      // 添加非匹配部分
      if (index > lastIndex) {
        parts.push({
          text: text.substring(lastIndex, index),
          highlight: false
        });
      }

      // 添加匹配部分
      parts.push({
        text: text.substring(index, index + keyword.length),
        highlight: true
      });

      lastIndex = index + keyword.length;
      index = text.indexOf(keyword, lastIndex);
    }

    // 添加剩余的非匹配部分
    if (lastIndex < text.length) {
      parts.push({
        text: text.substring(lastIndex),
        highlight: false
      });
    }

    return parts;
  }
});