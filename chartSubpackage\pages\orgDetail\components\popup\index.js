import moment from "../../../../../utils/moment";
// 机构详情 -> 年份选择
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    visible: {
      type: Boolean,
      required: false,
      default: () => false,
    
    },
    dataObj: {
      type: Object,
      required: false,
      default: () => ({}),
      observer(val){
        const this_ = this;
        this.setData({
          checkData: val
        }, () => {
          this_.initTime(); 
        })
      }
    },
  },
  lifetimes: {
    created(){
      this.initTime();
    }
  },
  data: {
    checkData: {},
  },
  methods: {
    initTime(){
      const timeData = []
      const current = moment().format("YYYY-MM-DD");
      const list = [1,3,5,10], title=['近1年', '近3年', '近5年', '近10年'];
      list.forEach((item, index) => {
        const obj = { title: '', value: ''}
        const arr = [moment(moment().subtract(item, 'year')).format("YYYY-MM-DD"), current];
        obj.title = title[index];
        obj.value  = arr.join(',');
        timeData.push(obj)
      });
      this.setData({ timeData });
    },
    isShowOpen(event) {
      const { touch } = event.target.dataset;
      if (touch === "true") {
        // this.setData({ checkData: {} });
        this.triggerEvent("isShowOpen", this.data.checkData);
      }
    },
    // 年份选择
    handleYearChange({ currentTarget: { dataset } }) {
      const this_ = this;
      const { item } = dataset
      const { checkData } = this.data
      this.setData({
        checkData: checkData.value === item.value ? {} : item
      }, () => {
        this_.triggerEvent("isShowOpen", this.data.checkData);
      })
    },
  }
})
