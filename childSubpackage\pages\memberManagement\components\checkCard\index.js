
import { passAuditUser, rejectAuditUser, applyDelete } from '../../../../../service/user';
const app = getApp();
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    item: {
      type: Object,
      value: {},
      observer(val){
        this.setData({ data: val })
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    delete(){
      const this_ = this;
      const { data } = this.data;
      applyDelete(data.id).then(() => {
        app.showToast('删除成功');
      }).finally(() => {
        this_.triggerEvent('delete');
      });
    },
    handleRefused(){
      const this_ = this;
      const { data } = this.data;
      rejectAuditUser(data.id).then(() => {
        app.showToast('已拒绝加入');
      }).finally(() => {
        this_.triggerEvent('refused');
      });
    },
    handleConsent(){
      const this_ = this;
      const { data } = this.data;
      passAuditUser(data.id).then(() => {
        app.showToast('加入成功');
      }).finally(() => {
        this_.triggerEvent('consent');
      });
    }
  }
})
