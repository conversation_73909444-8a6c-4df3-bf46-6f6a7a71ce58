.allWrap {
  background: #F4F4F4;
  height: 100vh;
  /* overflow-y: scroll; */
  scroll-behavior: smooth;
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

.outer {
  width: 100%;
  border: 1px solid red;
  margin: 0 auto;
}

.inner {
  width: 100%;
  height: 100%;
}

movable-area {
  width: 100%;
  height: 100%;
}

.item-move {
  display: inline-block;
  border: 1px solid #eee;
}

.copyitem {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  text-align: center;
  opacity: 0.5;
}

.copyitem image {
  width: 60rpx;
  height: 60rpx;
}

.copyitem view {
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  text-align: center;
  padding-top: 16rpx;
}

/*  */
.wrap {
  padding: 32rpx 24rpx;
  background: #fff;
  margin-top: 20rpx;
}

.tit {
  font-size: 28rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
  padding-bottom: 24rpx;
}
.tit text {
  font-size: 24rpx;
font-family: PingFang SC-Regular, PingFang SC;
font-weight: 400;
color: #9B9EAC;
margin-left: 16rpx;
}
.cont {
  display: flex;
  flex-wrap: wrap;
  background: #fff;
}

/* item */
.cont .item {
  display: flex;
  flex-direction: column;
  /* justify-content: center; */
  align-items: center;
  width: 20%;
  text-align: center;
  margin-bottom: 26rpx;
  padding-top: 8rpx;
}

.cont .item image {
  width: 60rpx;
  height: 60rpx;
}

.cont .item view {
  font-size: 24rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  text-align: center;
  padding-top: 16rpx;
}

/* input */
.searbox {
  width: 100%;
  background: #fff;
  padding: 32rpx 24rpx;
  height: 136rpx;
  position: fixed;
  background: #fff;
  z-index: 1;
}

.zhanwei {
  height: 136rpx;
}

.searchs {
  position: relative;
  z-index: 31;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #F4F4F4;
  border-radius: 8rpx;
  padding: 16rpx 0 16rpx 28rpx;
}

.s-input {
  display: flex;
  align-items: center;
  flex: 1;
}

.s-input-img {
  width: 36rpx;
  height: 36rpx;
}

input {
  caret-color: #E72410;
  color: #9B9EAC;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
}

.s-input-item {
  display: flex;
  align-items: center;
  position: relative;
  flex: 1;
  height: 40rpx;
  padding-left: 16rpx;
  /* border-right: 1px solid hsla(229, 9%, 64%, 0.5); */
  /* border: 1px solid red; */
}


.s-input-item-i {
  position: relative;
  flex: 1;
  height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #05070c;
}

.placeholder {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #9b9eac;
  line-height: 40rpx;
}

.search-cancel {
  height: 40rpx;
  line-height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: RIGHT;
  color: #20263a;
  padding: 0 28rpx;
}

.input-clear {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
}

/* 文字高亮 */
.heightItem {
  width: 100%;
  height: 40rpx;
}

.text {
  display: inline;
  font-size: 32rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #20263a;
}

.activetext {
  color: #E72410 !important;
}

.activetexts {
  color: rgba(231, 36, 16, 1) !important;
}

.null {
  height: calc(100vh - 136rpx);
  width: 100%;
  background: #fff;
  display: flex;
  align-items: center;
  flex-direction: column;
}

.null image {
  width: 230rpx;
  height: 230rpx;
  margin-bottom: 24rpx;
  padding-top: 150px;
}

.null text {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #74798c;
}

.scroll {
  height: calc(100vh - 136rpx);
}