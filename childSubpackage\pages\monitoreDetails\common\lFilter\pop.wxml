<template name="pop">
  <view class="container container_hd   {{isShow ? 'show' : 'disappear'}}" style="height: {{popResH+2}}px; top: {{popTopH-2}}px;" catchtap="yuqincloseHyFilter">
    <view class='z-height'>
      <view class="sort-list">
        <scroll-view scroll-y="{{true}}" style="padding: 0 24rpx 0 32rpx;">
          <view wx:for="{{sourceData.children}}" wx:key="index" class="sort_list {{ item.show?'active':'' }}" catchtap="{{ChooseMethod}}" data-item="{{item}}" data-source='{{dataSourceName}}' data-shows="{{dataShows}}">
            <text>{{item.name}}</text>
            <image data-item="{{index}}" wx:if="{{item.show}}" src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/pickon.png"></image>
          </view>
        </scroll-view>
      </view>
    </view>
  </view>
</template>

<!-- isShow,popResH,popTopH,sourceData-->
<!-- 字符串 -->
<!-- ChooseMethod,dataSourceName,dataShows -->
<!-- <template is='pop' data="{{
    popResH,popTopH,
    isShow:polishingOne,
    sourceData:polishingOneData,
    ChooseMethod:'polishingChoose',
    dataSourceName:'polishingOneData',
    dataShows:'polishingOne_val'
  }}"></template> -->