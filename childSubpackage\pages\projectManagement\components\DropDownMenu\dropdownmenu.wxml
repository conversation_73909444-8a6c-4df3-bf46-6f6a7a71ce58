<view class="dropdown-menu">
  <!-- 下拉头部 -->
  <view class="menu-head">
    <DropDownHead 
      checked="{{checkedAll}}" 
      hasCheckbox="{{hasCheckbox}}" 
      bindselect="handleMenuSelect" 
      bindcheck="handleCheckedAll"
      headGroup="{{headGroup}}"
    ></DropDownHead>
  </view>
  <!-- 筛选 -->
  <TagMultipleChoice
    visible="{{filterVisible}}"
    dataList="{{filterList}}"
    top="{{44}}"
    bindsubmit='handleFilter'
    bindhandlequickQuantity="handleQuickQuantity"
    bindhandlecollectDate="handleCollectDate"
    bindsetVipVisible="setVipVisible"
    bindclose="reset"
  >
    <!-- 自定义数量文本框 -->
    <view slot="customQuantity" class="custom-quantity-wrap"> 
      <view>从</view>
      <view class="custom-quantity textBox">
        <input class="year" placeholder="开始数量" data-type="minNum" bindinput="handleInput" value="{{minNum}}" ></input>
        <text class="short-line">—</text>
        <input class="year" placeholder="结束数量" data-type="maxNum" bindinput="handleInput" value="{{maxNum}}" ></input>
      </view>
    </view>
    <!-- 收藏日期文本框 -->
    <view slot="collectDate" class="collectDate textBox" >  
      <view class="year" data-type="collectDate-startTime" bindtap="showDatePicker">{{startTime ||'开始时间'}}</view>
      <text class="short-line">—</text>
      <view class="year" data-type="collectDate-endTime" bindtap="showDatePicker">{{endTime || '结束时间'}}</view>
    </view>
  </TagMultipleChoice>

  <!-- 时间选择 -->
  <DatePicker
    visible="{{showPicker}}"
    _date="{{backfillDate}}"
    dateType="{{dateType}}"
    bindsetDate="setDate"
    title="{{title}}"
  >
  </DatePicker>

</view>