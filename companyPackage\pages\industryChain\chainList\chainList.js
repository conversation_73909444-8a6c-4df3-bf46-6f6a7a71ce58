import {
  chain,
  common
} from '../../../../service/api';
import {
  setTagColor,
  preventActive
} from '../../../../utils/util.js'
import {
  hasPrivile
} from '../../../../utils/route'
import {
  getHeight
} from '../../../../utils/height.js'
import {
  clearChildComponent,
} from '../../../../components/huntCopy/mixin'
import {
  collect
} from '../../../../utils/mixin/collect'
import {
  handleShareUrl,
  getShareUrl
} from '../../../../utils/mixin/pageShare'
const app = getApp();
Page({
  data: {
    company_num: 0,
    //请求的相关
    paramsNew: {
      chain_codes: [],
      page_index: 1,
      page_size: 10
    },
    oldParams: {}, //用来反向填充的
    requestData: [],
    hasData: true, // 判断接口是否还有数据返回
    isTriggered: false, // 下拉刷新状态

    cnacelEnt: '', //缓存取消收藏企业id
    popIndex: 0, //缓存取消收藏企业下标
    // 联系方式
    showContact: false,
    contactList: [], //联系方式列表
    // 地址
    showAddress: false,
    addmarkers: [],
    locationTxt: '',
    location: {
      lat: '',
      lon: '',
    },
    locationMap: {},
    chain_codes: [],
    defaultChainVal: '',
    dropDownMenuTitle: ['所属地区', '产业类型', '更多筛选'],
    activeEntId: '', // 当前点击企业的id
    isLogin: app.isLogin(),
    entUnlogin: true, // 是否是在未登录的情况点击了企业标题
  },
  onLoad: function (options) {
    handleShareUrl()
    let {
      chainName = "政府数据", chain_code
    } = options
    wx.setNavigationBarTitle({
      title: chainName
    })
    let {
      paramsNew
    } = this.data
    // 这个不管怎样都要带上 所以写死
    paramsNew.chain_codes = [chain_code]

    this.setData({
      paramsNew,
      defaultChainVal: chain_code,
      chain_codes: [chain_code],
      'dropDownMenuTitle[1]': chainName.length >= 4 ? chainName.slice(0, 4) + '...' : chainName,
      chainName: chainName
    })
  },
  // 下拉刷新
  handleRefresher() {
    let {
      paramsNew
    } = this.data;
    paramsNew.page_index = 1;
    this.setData({
      paramsNew,
      requestData: [],
      isTriggered: true,
      hasData: true,
    })
    this.quest();
  },
  //加载更多
  async loadMore() {
    // 判断vip类型 主要是针对普通用户 
    if (this.data.requestData.length >= 10) {
      let permission = await hasPrivile({
        packageType: true
      })
      this.setData({
        permission,
      })
      if (permission == '普通VIP') return;
    }
    let {
      paramsNew,
      hasData
    } = this.data;
    if (!hasData) return;
    paramsNew.page_index += 1;
    this.setData({
      paramsNew
    });
    this.quest(true);
  },
  goMap() {
    const {
      locationMap
    } = this.data
    wx.openLocation(locationMap)
  },
  onCloseAddress() {
    this.setData({
      showAddress: false
    })
  },
  async quest(bl) {
    wx.showLoading({
      title: '正在加载',
      mask: true
    })
    let {
      paramsNew,
      hasData,
      requestData
    } = this.data
    paramsNew['chain_codes'] = this.data.chain_codes
    hasData = true
    let {
      items,
      count
    } = await chain.chainDetail(paramsNew);
    items = items.map(item => {
      item.tags = setTagColor(item.tags);
      return item;
    })
    items.forEach(item => {
      item.tags = item.tags.splice(0, 3)
    })
    if (items.length < paramsNew.page_size || count == paramsNew.page_size) hasData = false;
    this.setData({
      hasData,
      company_num: count,
      requestData: bl ? requestData.concat(items) : items,
      isTriggered: false
    })
    wx.hideLoading();
  },
  // 点击企业标题
  handleTit() {
    const {
      isLogin
    } = this.data;
    this.setData({
      entUnlogin: !isLogin
    });
  },
  onCloseContact() {
    this.setData({
      showContact: false
    })
  },
  onShow() {
    const {
      entUnlogin
    } = this.data;
    this.handleHeight();
    entUnlogin && this.quest();
  },
  // 顶部筛选
  onFlitter(e) {
    let {
      dropDownMenuTitle,
      paramsNew,
      chain_codes
    } = this.data
    const obj = e.detail;
    const len = obj.chain_codes.length;
    obj.name1 && (dropDownMenuTitle[0] = obj.name1);
    obj.name2 && (dropDownMenuTitle[1] = obj.name2);
    console.log('obj', obj)
    let name = obj.chain_codes[0]?.name == '全部' ? obj.chain_codes[0]?.parent_name : obj.chain_codes[0]?.name
    this.setData({
      dropDownMenuTitle,
      chainName: name || this.data?.chainName,
      chain_codes: len ? [obj.chain_codes[0].code] : chain_codes
    });
    wx.setNavigationBarTitle({
      title: name || this.data?.chainName
    })
    delete obj['name1'];
    delete obj['name2'];
    delete obj.isFilter;
    paramsNew = {
      ...paramsNew,
      ...obj
    };
    this.setData({
      paramsNew
    });
    this.handleRefresher();
  },
  // 卡片点击回调 
  async onCard(data) {
    let that = this
    preventActive(this, async () => {
      // 地址  "site"  联系方式 "relation" 官网 "official" 收藏 "collect" 四个字段对呀四个方法逻辑-具体见设计图
      const type = data.detail.type
      const comDetail = data.detail.data
      // 处理收藏 
      if (type == 'collect') {
        comDetail.tags = comDetail.tags.map(tag => tag.tagName);
        collect(that, comDetail, 'requestData')
      } else if (type == 'relation') {
        this.setData({
          activeEntId: comDetail.ent_id,
          showContact: true
        });
      } else if (type === 'site') {
        this.setData({
          location: {
            lat: +comDetail.location.lat,
            lon: +comDetail.location.lon,
          },
          locationTxt: comDetail.register_address,
          addmarkers: [{
            id: 1,
            latitude: +comDetail.location.lat,
            longitude: +comDetail.location.lon,
            iconPath: "https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/marker.png",
            width: 20,
            height: 20,
          }],
          showAddress: true,
          locationMap: {
            latitude: +comDetail.location.lat, //维度
            longitude: +comDetail.location.lon, //经度
            name: comDetail.register_address, //目的地定位名称
            scale: 15, //缩放比例
            address: comDetail.register_address //导航详细地址
          }
        })
      }
    })
  },
  // 动态获取蒙层高度
  handleHeight() {
    const that = this
    getHeight(that, '.page_head', (data) => {
      // console.log("计算蒙层高度", data)
      this.setData({
        computedHeight: data.screeHeight - data.res[0].height,
        isLogin: app.isLogin()
      })
    })
  },

  onexpPop() {
    this.setData({
      isExpPop: true
    })
  },

  // 重置筛选条件
  resetCondition() {
    clearChildComponent(this)()
  },
  getParams(e) {
    // 筛选后获取的数据
    let {
      isHeight,
      paramsData
    } = e.detail
    this.setData({
      isHeight,
      paramsData
    })
  },
  vipPop(val) {
    this.setData({
      vipVisible: val
    })
  },
  onShareAppMessage: function () {
    console.log(this.data.chain_codes, this.data.chainName, 111)
    return {
      title: `邀请你查看${this.data.company_num}家${this.data.chainName}企业名单`, //自定义转发标题
      path: getShareUrl(`/companyPackage/pages/industryChain/chainList/chainList?chain_code=${this.data.chain_codes[0]}&chainName=${this.data.chainName}`), //分享页面路径
      imageUrl: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/h4.png' //图片后面换
    };
  },
});