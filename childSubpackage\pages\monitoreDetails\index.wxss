@import '/template/null/null.wxss';
@import '/template/more/more.wxss';
@import '/template/tabBar/index.wxss';
@import './common/lFilter/index.wxss';

.home-wrap {
  position: relative;
  width: 100%;
  height: 100vh;
  /* overflow: hidden; */
  overflow-y: scroll;
  background: #f7f7f7;
}

.home {
  width: 100%;
  overflow: hidden;
}

::-webkit-scrollbar {
  display: none;
  width: 0;
  height: 0;
  color: transparent;
}

/* 头部样式 */
.h_head {
  position: relative;
  width: 100%;
  height: 374rpx;
  /* background: rgba(231, 36, 16, 1); */
  /* padding-top: 1px; */
  border-bottom: 20rpx solid #F7F7F7;
  padding-top: 36rpx;
}

.head-top {
  position: relative;
  height: 346rpx;
  width: 100%;
  background: #fff;
  border-radius: 40rpx 40rpx 0rpx 0rpx;
  border-bottom: 20rpx solid #f7f7f7;
}

.card-boxs {
  height: 100rpx;
  padding: 32rpx 32rpx 8rpx;
  display: flex;
  align-items: center;
}

.card-logo .img {
  width: 60rpx;
  height: 60rpx;
  margin-right: 16rpx;
}

.h_head image {
  width: 100%;
  height: 100%;
}

.h_head .head_bg {
  position: absolute;
  left: 0;
  top: 0;
  height: 68px;
  width: 100%;
}

.card_c {
  display: flex;
  justify-content: space-around;
  padding: 24rpx 0 20rpx;
  height: 134rpx;
  /* border: 1px solid blue; */
}

.card_c_i {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.zj {
  flex: 1.336;
}

.card_c_is::before {
  position: absolute;
  content: "";
  right: 0;
  /* top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%); */
  top: 6rpx;
  border-right: 1px solid #EEEEEE;
  width: 0;
  height: 48rpx;
}

.card_c_is::after {
  position: absolute;
  content: "";
  left: 0;
  /* top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%); */
  top: 6rpx;
  border-right: 1px solid #EEEEEE;
  width: 0;
  height: 48rpx;
}

.card_c_i .name {
  font-size: 24rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: CENTER;
  color: #74798C;
  padding-bottom: 12rpx;
}

.card_c_i .cont {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: CENTER;
  /* color: #74798c; */
  color: #20263A;
}

.card_c_i .contblue {
  /* color: #74798C; */
  color: #1E75DB;
}

/* 那根横线 后面在调 */
.card_ico {
  position: relative;
  display: flex;
  justify-content: flex-end;
  flex-wrap: nowrap;
  height: 92rpx;
  align-items: center;
  padding-right: 24rpx;
}

.card_ico::after {
  content: " ";
  width: calc(100% + 48rpx);
  height: 1px;
  background: #eee;
  /* background: red; */
  position: absolute;
  top: 0;
  left: -24rpx;
  transform: scaleY(0.5);
}

.card_ico_i {
  display: flex;
  justify-content: center;
  align-items: center;
  /* background: rgba(7, 110, 228, 0.06); */
  border: 1rpx solid #DEDEDE;
  border-radius: 8rpx;
  margin-left: 40rpx;
  font-size: 24rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  color: #20263A;
  height: 52rpx;
  padding: 0 12rpx;
}

.card_ico_i_img {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 4rpx;
}

/* 地址导航 */
.dialog-con .map {
  position: relative;
  width: 100%;
  line-height: 108rpx;
  text-align: center;
  font-size: 34rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #E72410;
}

.dialog-con .map::after {
  content: " ";
  width: 200%;
  height: 1rpx;
  background: #E5E5E5;
  position: absolute;
  top: 0;
  left: 0%;
  transform: scaleY(0.5);
}

.dialog-con .cancel {
  position: relative;
  height: 112rpx;
  line-height: 112rpx;
  text-align: center;
  font-size: 34rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
}

.dialog-con .cancel::after {
  content: " ";
  width: 200%;
  height: 1rpx;
  background: #E5E5E5;
  position: absolute;
  top: 0;
  left: 0%;
  transform: scaleY(0.5);
}

.container_hd {
  position: absolute !important;
}

.sci_heads .nav {
  background: #fff !important;
}