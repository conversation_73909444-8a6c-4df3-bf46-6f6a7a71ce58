<import src="/template/menuhead/index"></import>
<view class="head">
  <template is='menu-head' data="{{selected_source_name,dropDownMenuTitle,district_open,source_open,filter_open,selected_filter_name,selected_source_name,district_val,source_val,filter_val }}"></template>
</view>

<!-- 具体内容 -->
<!-- 地区 -->
<region-selection visible="{{district_open}}" top="{{top}}" bindsubmit="getRegion" bindclose="closeRegion" oldData="{{regionData}}" />

<!-- 行业 -->
<!-- <industry-select visible="{{source_open}}" top="{{top}}" oldData="{{industryData}}" bindsubmit="getIndustry" dataType='eleseicAry' bindclose="closeRegion" /> -->

<!-- 产业链 -->
<SingleSelect visible="{{source_open}}" defaultVal="{{selectChainVal}}" allData="{{allChainData}}" bindselect="handleSelectChain"></SingleSelect>

<!-- 更多筛选 -->
<view class="container container_hd {{filter_open ? 'show' : 'disappear'}} line-tesu" style="height: {{computedHeight}}px; overflow: hidden;">

  <view style="height: {{computedHeight-(isIphoneX ? 85:55)}}px;" class="searWrap">
    <scroll-view view scroll-y style="height: {{computedHeight-(isIphoneX ? 85:55)}}px;" scroll-with-animation class="searWrap-r">
      <view style="height: auto;">
        <view class="searL" wx:for="{{leftList}}" wx:key="index">
          <view class="{{item.isActive&&'active'}}" wx:if="{{ item.onlyText}}">
            <view class="tit" bindtap="closeleft" data-item="{{item}}">
              <view>{{item.title}}</view>
              <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/h-down.png" mode="aspectFill" wx:if="{{!item.isOpen}}"></image>
              <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/h-upward.png" mode="aspectFill" wx:else></image>
            </view>
            <view class="tit_cont" style="height: {{item.isOpen?'auto':0}};">
              <view wx:for="{{item.map}}" wx:for-index="key" wx:for-item="itm" wx:key="key" class="{{itm.active&&'child-active'}}" bindtap="leftactvie" data-item="{{item}}" data-itm="{{itm.key}}">{{itm.value}}</view>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
    <scroll-view scroll-y style="height: {{computedHeight-(isIphoneX ? 85:55)}}px;" scroll-with-animation class="searWrap-l" scroll-into-view="{{idName}}">
      <view class="content">
        <view wx:for="{{itemList}}" wx:key="id" id="{{item.type}}" bindtap="closeInptPop">
          <!-- inputu -->
          <view class="content-item-pop cnt-ipt" wx:if="{{item.special=='input'}}" catchtap="_event">
            <view class="title" style="font-weight: {{idName==item.type?600:400}};">
              {{item.title}}
            </view>
            <view class="ipt">
              <input placeholder="请输入企业名称(非必填)" placeholder-class="placeCls" value="{{params.ent_name}}" bindfocus="onFocus" bindblur="onBlur" bindinput="onInput" maxlength="{{24}}" />
            </view>
            <!-- 搜索框  -->
            <view class="child-box" wx:if="{{focus&&params.ent_name.length>0}}" catchtouchmove="return">
              <scroll-view class="child-box-ul" scroll-y scroll-with-animation>
                <block wx:for="{{searchList}}" wx:key="index" wx:for-item="obj" wx:if="{{searchList.length>0}}">
                  <view hover-class="hover">
                    <view class="search-li" catchtap="clickItem" data-item="{{obj}}">
                      <block wx:for="{{obj.ent_name}}" wx:key="index">
                        <text class="listtext {{item==form.company_name ? 'searchHigh' : '' }}">{{item}}</text>
                      </block>
                    </view>
                  </view>
                </block>
                <block wx:if="{{searchList.length<=0}}">
                  <view class="loading">
                    <view class="weui-loading size"></view>
                    <view class="text">{{text ||'努力加载中...'}}</view>
                  </view>
                </block>
              </scroll-view>
            </view>
          </view>

          <!-- 弹窗 -->
          <view class="content-item-pop" wx:elif="{{item.special=='pop'}}">
            <!-- isPage&&(item.title=='所属行业') -->
            <view class="title" style="font-weight: {{idName==item.type?600:400}};">
              {{item.title}}
            </view>
            <view class="content-item-pop-r" bindtap="handlePop" data-item="{{item}}">
              <!-- 后续渲染成对应的 -->
              <text class="{{item.content&&'has'}}">{{item.content || '全部'}}</text>
              <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home_arrow.png"></image>
            </view>
          </view>
          <!-- 列表 -->
          <view class="'content-item' {{item.title=='产业优选'&&'yxbg'}} {{item.title=='龙头企业'&&'yxbglt'}} {{(item.title=='疑似扩张' || item.title=='企业规模'|| item.title=='所属产业' )&&'yxbglt1'}} {{item.title=='科技型企业'&&'yxbgend'}}" wx:else>
            <!--  如果是产业优选背景不一样 操 产业优选的样式 卧槽他妈23.2.23  -->
            <view class="yxbgImg" wx:if="{{item.title=='产业优选'}}">
              <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/zsyx.png"></image>
            </view>
            <!-- 这里有三种情况 -->
            <view class="tit-text" wx:if="{{item.onlyText}}">
              <view wx:if="{{item.title!='产业优选'}}"></view> {{item.title}} <view wx:if="{{item.title!='产业优选'}}"></view>
            </view>
            <!-- 样式结束 -->
            <view class="tit" wx:elif="{{!item.onlyText&&!item.isOpenIcon}}" style="font-weight: {{idName==item.type?600:400}};">
              {{item.title}}
              <text wx:if="{{item.icon}}" bindtap="onexpPop" class="filterexp"></text>
              <view wx:if="{{item.vip}}" class="vip"></view>
            </view>
            <view wx:elif="{{item.isOpenIcon}}" class="zhankai" style="font-weight: {{idName==item.type?600:400}};">
              <view style="display: flex; align-items: center;">
                {{item.title}}
                <view wx:if="{{item.vip}}" class="vip"></view>
              </view>
              <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/h-upward.png" mode="aspectFill" wx:if="{{!item.isOpen}}" bindtap="openright" data-item="{{item}}"></image>
              <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/h-down.png" mode="aspectFill" wx:else bindtap="openright" data-item="{{item}}"></image>
            </view>
            <!-- 174rpx -->
            <view class="wrap" style=" height: {{item.isOpenIcon&&item.isOpen ? '174rpx':'auto' }};">
              <text data-type="{{item.type}}" data-id="{{child.id}}" bindtap="selectTag" data-item="{{item}}" class="{{child.active && 'active'}}" wx:for="{{item.list}}" wx:key="id" wx:for-item="child" data-name="{{child.name}}">{{child.name}}</text>
              <view class="input-wrap" wx:if="{{item.unit}}">

                <!-- 注册资本输入框 -->
                <view class="{{capitalActive && 'active'}}" wx:if="{{item.genre=='input'&&item.type=='register_capital'}}">
                  <input type="number" model:value="{{minCapital}}" bindinput="inputChange" bindfocus="inputFocus" maxlength="{{10}}" placeholder="{{item.min}}" placeholder-class="plc" data-type="{{item.type}}">
                  </input>
                  <text class="short-line">—</text>
                  <input type="number" model:value="{{maxCapital}}" bindinput="inputChange" bindfocus="inputFocus" maxlength="{{10}}" placeholder="{{item.max}}" placeholder-class="plc" data-type="{{item.type}}">
                  </input>
                </view>

                <!-- 参保人数 -->
                <view class="{{socialActive && 'active'}}" wx:if="{{item.genre=='input'&&item.type=='super_dimension_social_num'}}">
                  <input type="number" model:value="{{socialminPeson}}" bindinput="inputChange" bindfocus="inputFocus" maxlength="{{5}}" placeholder="{{item.min}}" placeholder-class="plc" data-type="{{item.type}}">
                  </input>
                  <text class="short-line">—</text>
                  <input type="number" model:value="{{socialmaxPeson}}" bindinput="inputChange" bindfocus="inputFocus" maxlength="{{5}}" placeholder="{{item.max}}" placeholder-class="plc" data-type="{{item.type}}">
                  </input>
                </view>


                <!-- 注册时间输入框 -->
                <view class="{{dateActive && 'active'}}" wx:if="{{item.genre=='tpop'}}">
                  <view class="year" data-type="startDate" bindtap="showtDatePicker">{{minDate || item.min}}</view>
                  <text class="short-line">—</text>
                  <view class="year" data-type="endDate" bindtap="showtDatePicker">{{maxDate || item.max}}</view>
                </view>
                <text>{{item.unit}}</text>
              </view>
            </view>
          </view>

        </view>
      </view>
    </scroll-view>
  </view>

  <view class="footer" style="height: {{isIphoneX?'85px':'55px'}};">
    <text class="reset" bindtap="clearSear">重置</text>
    <text bindtap="search">确定</text>
  </view>
</view>


<!-- 企業类型 -->
<multiplec-choice visible="{{enttypePop}}" mark="ent_type" dataType="enttypeAry" bindsubmits="submitSub" oldData="{{params.enttype_data}}"></multiplec-choice>
<!-- 企业许可 -->
<multiplec-choice visible="{{districtPop}}" mark="ent_cert" dataType="allCertAry" bindsubmits="submitSub" oldData="{{params.all_cert_data}}"></multiplec-choice>
<!-- 最高年限   dateType="{{dateType}}" _date="{{date}}" bindsetDate="setDate" changePadding="{{true}}"-->
<DatePicker visible="{{datePop}}" _date="{{date}}" bindsetDate="setDate" changePadding="{{true}}" />
<!-- 企业类型说明弹窗 -->
<half-screen-pop title="提示" position="bottom" zIndex="{{99}}" showCancelBtn="{{false}}" showCancelCls visible="{{isexpPop}}" confirmBtnText="知道了">
  <view class="expPop" slot="customContent">
    根据不同招商场景，对企业规模进行的等级划分，大型、特大型企业适合政府园区招商；中小型、中型企业适合民营园区招商；微型、小微型企业可能存在高成长性企业
  </view>
</half-screen-pop>


<!-- capture-catch:touchmove="preventdefault" -->