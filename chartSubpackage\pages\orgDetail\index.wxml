<import src="/template/tabBar/index"></import>
<import src="/template/null/null"></import>
<view class="org_detail_wrapper" capture-bind:tap="showPreferenceInfo" data-type="close" >
  <CustomNavbar text="机构详情" id="navigationBar" class="navigationBar" textPositon="center" navColor="{{['rgba(255, 255, 255, 1)', 'rgba(255, 255, 255, 1)']}}" showNav navBg="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/other/top_bg2.png" navBg0="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/other/statusbg.png" />


  <scroll-view style="height: {{scrollViewHeight}}px" scroll-y scroll-with-animation throttle="{{false}}">
    <!-- 顶部内容 -->
    <view id="header" style="display: {{ contentT > 0 ? 'block' :'none'}};">
        <image class="head_bg" src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/other/top_bg3.png" mode="aspectFill"></image>
        <view class="header-box">
          <view class="top">
            <image class="img" src="{{ obj.org_logo ? obj.org_logo : 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/default.png' }}" binderror="errorFunction" mode="aspectFill" />
            <view class="top_right"  >
              <view>{{ obj.org_name }}</view>
              <view wx:if="{{ obj.cat_names.length }}">
                <view class="tag_box">
                  <block wx:if="{{ index < 3 }}" wx:for="{{obj.cat_names}}" wx:key="index">
                    <text>{{ item }}</text>
                  </block>
                </view>
                <view class="red_right" bindtap="openDialog" data-type="tag" data-title="更多赛道" wx:if="{{ obj.cat_names.length > 3 }}">更多
                  <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/red_right.png" mode="aspectFill" />
                </view>
              </view>
            </view>
          </view>
          <view class="mind">
            <view class="item">
              <text>投资笔数： </text>
              <text> {{ obj.inv_count }} </text>
            </view>
            <view class="item">
              <text>关联企业： </text>
              <view class="com_name">
                <text class="link" wx:if="{{entList.length > 0}}" bindtap="goToEntDetail" data-item="{{entList[0]}}" >{{ entList[0].ent_name  }}</text>
              <text wx:else> ---</text>
              <view class="red_right" wx:if="{{entList.length > 1}}" bindtap="openDialog" data-title="关联企业" data-type="ent">更多
                  <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/red_right.png" mode="aspectFill" />
                </view>
              </view>
            </view>
          </view>
        </view>
    </view>
    <view id="content" style="margin-top:{{contentT}}rpx; ">
      <!-- 横向滚动 -->
      <scroll-view id="swipe_tab" class="swipe_tab" scroll-x="true"  scroll-left="{{ scrollLeft }}">
        <view id="{{item.sectionId}}" class="tab_item {{ currentSwiper == item.sectionId && 'tab_item_active' || item.sectionId }}" wx:for="{{swiperData}}" wx:key="key" data-item="{{item}}">
          <text bindtap="handleTabChange" data-item="{{item}}"> {{ item.value }} </text>
        </view>
      </scroll-view>
      <!-- 子滑动内容 -->
      <scroll-view style="height: {{subScrollViewHeight}}px;" scroll-y scroll-with-animation scroll-into-view="{{currentSwiper}}" scroll-with-animation throttle="{{false}}" bindscroll="onScroll" >
        <Wrapper id="section1" title="投资数量" marginTop="0rpx">
        <view slot="content" class="chart_box">
          <view style="width: 100%; height:486rpx;" wx:if="{{dataLine.legend.length > 0}}">
            <LineChart id="line-charts" unit="万" legend="{{false}}" showYName="{{true}}" data="{{dataLine}}"  />
          </view>
          <!--暂无数据-->
          <view wx:if="{{dataLine.legend.length <= 0}}" style="width: 100%;height:100px">
            <template is='null'></template>
          </view>
        </view>
      </Wrapper>
      <Wrapper id="section2" title="投资偏好" marginTop="20rpx">
        <view slot="leftContent" class="preferenceInfo">
          <image bindtap="showPreferenceInfo" style="width: 32rpx;height: 32rpx;" src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/yiwen.png" mode="aspectFill"></image>
          <view class="tips" style="display: {{ isPreference ? 'block': 'none'}};">
            <text>当前时间段投资次数前十的赛道</text>
          </view>
        </view>
        <view slot="rightContent" style="display: flex;  align-items: center; font-size: 28rpx;color: #20263A; font-weight: 400;" bindtap="changeYear">
         {{ oldTimeData.title || '全部年份'}}
          <view style="display: flex; align-items: center; margin-left: 8rpx;">
            <image style="width: 32rpx;height: 32rpx;" src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/menu-down.png" mode="aspectFill">
            </image>
          </view>
        </view>
        <view slot="content" class="chart_box" >
          <view style="width: 100%; height: 500rpx" wx:if="{{treeMapData.length > 0}}">
            <RectTreeMap canvasId="treeMapId" data="{{treeMapData}}" colors="{{defaultColors}}"/>
          </view>
          <!--暂无数据-->
          <view wx:if="{{treeMapData.length <= 0}}" style="width: 100%; height:100px">
           <template is='null'></template>
          </view>
        </view>
      </Wrapper>
      <Wrapper id="section3" title="轮次分布" marginTop="20rpx">
        <view slot="content" class="chart_box" >
         <view style="height: 674rpx; width:100%;">
          <PieChart canvasId="pieChart1" data="{{turnList}}" colors="{{colorsArr}}" seriesBottom="0"/>
          <!--暂无数据-->
          <view wx:if="{{turnList.length <= 0}}" style="width:100%; height: 574rpx">
            <template is='null'></template>
          </view>
         </view>
        </view>
      </Wrapper>
      <Wrapper id="section4" title="地区分布" marginTop="20rpx">
        <view slot="rightContent" class="map_right">
          <view class="{{mapActive === 'province'&& 'map_active1' || 'defulat1'}}" bindtap="changeMapActive" data-type="province"> <text> 按省份</text> </view>
          <view class="{{mapActive === 'city'&& 'map_active2' ||'defulat2'}}" bindtap="changeMapActive" data-type="city"> <text>按城市</text> </view>
        </view>
        <view slot="content" class="chart_box" >
          <view style="width: 100%;" wx:if="{{areaList.length >0 }}">
            <view class="map">
              <Map heatMapData="{{allAareaList }}" bindclickMap="clickMap" region="{{region}}"/>
            </view>
            <ItemBox data="{{ areaList }}"/>
          </view>
          <!--暂无数据-->
          <view wx:if="{{areaList.length <= 0}}" style="width: 100%;height: 100px;">
            <template is='null'></template>
          </view>
        </view>
      </Wrapper>
      <Wrapper id="section5" title="投资事件" marginTop="20rpx">
        <view slot="leftContent" style="font-size: 28rpx; font-weight: 600;color: #74798C;margin-left: 8rpx;">
          {{eventCount}}笔
        </view>
        <view wx:if="{{eventCount > 3}}" slot="rightContent" style="display: flex;  align-items: center; font-size: 28rpx;color: #20263A; font-weight: 400;">
          <view class="red_right" bindtap="getMore" data-type="event" style="font-size: 24rpx ;">
            <text>查看更多</text>
            <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/red_right.png" mode="aspectFill" />
          </view>
        </view>
        <view slot="content">
          <view class="event_box" >
            <block wx:for="{{eventList}}" wx:key="index" data-item="{{item}}">
              <!-- 卡片 -->
              <Card obj="{{item}}" wx:if="{{index < 3}}" />
            </block>
             <!--暂无数据-->
             <view wx:if="{{eventList.length <= 0}}" style="width: 100%;height: 100px;">
              <template is='null'></template>
            </view>
          </view>
        </view>
      </Wrapper>
      <Wrapper id="section6" title="相关资讯" marginTop="20rpx" minHeight="60rpx">
        <view slot="rightContent" style="display: flex;  align-items: center; font-size: 28rpx;color: #20263A; font-weight: 400;" wx:if="{{informationCount > 5}}">
          <view class="red_right" bindtap="getMore" data-type="information" style="font-size: 24rpx ;">
            <text>查看更多</text>
            <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/red_right.png" mode="aspectFill" />
          </view>
        </view>
        <view slot="content" class="information">
          <view class="list_box">
            <block wx:for="{{information}}" wx:key="index" data-item="item">
              <view class="list" bindtap="handleCopy" data-item="{{item}}" wx:if="{{index < 5 }}">
                <image class="info_img" src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/info.png" mode="aspectFill"></image>
                <view class="info_title">{{ item.title }} </view>
                <view class="info_time"> {{ item.event_time }} </view>
              </view>
            </block>
            <!--暂无数据-->
            <view wx:if="{{informationCount <= 0}}" style="width: 100%;height: 100px;">
              <template is='null'></template>
            </view>
          </view>
        </view>
      </Wrapper>
      </scroll-view>
    </view>
  </scroll-view>

  <!-- dialog -->
  <Dialog title="{{dialogTitle}}" cancelBtnText="关闭" top="{{navHeight}}" isShowCancel="false" isShowConfirm="{{false}}" visible="{{openSet}}" bindclose="tagClose" showFooter="{{dialogType !== 'ent'}}" showCloseBtn="{{dialogType === 'ent'}}" zIndex="9999">
    <view class="dialog_content" wx:if="{{dialogType === 'tag'}}">
      <block wx:for="{{obj.cat_names}}" wx:key="index">
        <text>{{ item }}</text>
      </block>
    </view>
    <view class="dialog_content" wx:if="{{dialogType === 'ent'}}">
      <Table orgId="{{org_id}}" bindclose="tagClose"></Table>
    </view>
  </Dialog>
  <!-- popup -->
  <Popup visible="{{yearPopupVisible}}" dataObj="{{oldTimeData}}" bindisShowOpen="onHandlertrigger" />
</view>