<import src="/template/null/null"></import>

<!-- 滚动到对应位置用scrollView做  每次展开收起都要计算高度-->
<view class="wrap">
  <!-- 搜索框 -->
  <view class="searbox">
    <view class='searchs'>
      <view class="s-input">
        <view class="s-input-img">
          <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/search.png" mode="aspectFit"></image>
        </view>
        <view class="s-input-item {{ent_name.length&&'s-input-itemss'}}">
          <input class="s-input-item-i" type="text" placeholder='输入功能模块关键词搜索' placeholder-class='placeholder' bindblur='onBlur' value="{{ents_name}}" focus="{{inputShowed}}" bindinput="onInput" confirm-type='search' />
          <view hidden="{{ent_name.length <= 0}}" catchtap="onClear" class="input-clear">
            <view class="clearIcon"></view>
          </view>
        </view>
      </view>
      <view hidden="{{ent_name.length <= 0}}" class="search-cancel" bindtap="goBack">取消</view>
    </view>
  </view>
  <!-- 滚动展开部分 -->
  <view class="operation-wrapper">
    <scroll-view class="home" style="height: {{contentH}}px;" scroll-top="{{intoView}}" scroll-with-animation scroll-y refresher-triggered="{{fresher}}" refresher-threshold="{{25}}" bindrefresherrefresh="onfresher" refresher-enabled="{{false}}" will-change='transform'>
      <view wx:if="{{!ent_name}}">
        <view wx:for="{{operationList}}" wx:key="id" id="{{'id'+item.id}}" class="operation-item {{item._show?'show':''}}">
          <view class="operation-item-title item-row" data-index="{{index}}" data-item="{{item}}" bindtap="onHandlerShow">
            <text>{{ item.manual_class }}</text>
            <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/childSubpackage/image/operation/show.png" />
          </view>
          <view wx:for="{{item.res_vo_list}}" wx:key="item" class="operation-item-child item-row" data-item="{{item}}" bindtap="onHandlerClick">
            <text>{{item.manual_title}}</text>
            <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/childSubpackage/image/image/operation/hide.png" />
          </view>
        </view>
        <view wx:if="{{operationList.length === 0}}" class="null-wrapper" style="width: 100%;height: {{contentH}}px;">
          <template is='null'></template>
        </view>
      </view>
      <view class="operation-item" wx:else>
        <view wx:for="{{searchList}}" wx:key="item" class="operation-item-child item-row" data-item="{{item}}" bindtap="onHandlerClick">
          <view>
            <text wx:for="{{item.textNodes}}" wx:key="item" style="color:{{item.high&&'#E72410'}};">{{item.text}}</text>
          </view>
          <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/childSubpackage/image/operation/hide.png" />
        </view>
        <view wx:if="{{searchList.length <= 0}}" class="null-wrapper" style="width: 100%;height: {{contentH}}px;">
          <template is='null'></template>
        </view>
      </view>
    </scroll-view>
  </view>
</view>