.list {
  /* padding-bottom: 40rpx; */
}
.search-card-wrap {
  padding-bottom: 1rpx;
}
.search-card {
  width: 100%;
  background: #ffffff;
  padding: 32rpx 24rpx 16rpx;
  margin-bottom: 20rpx;
}

.search-card .card-boxs {
  display: flex;
  align-self: start;
}

.search-card .card-logo {
  width: 100rpx;
  height: 100rpx;
  flex-shrink: 0;
  box-shadow: 0px 0px 8rpx 0px rgba(32, 38, 58, 0.1);
  border-radius: 10rpx;
  margin-right: 24rpx;
  overflow: hidden;
  border-radius: 8rpx;
}

.card-head {
  width: 100%;
  height: 100rpx;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.card_h {
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  /* flex: 1; */
  /* margin-bottom: 18rpx; */
}

.card_h_l {
  width: 440rpx;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  font-size: 32rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 600;
  text-align: LEFT;
  color: #20263a;
}

/* .card_h_l text {
  color:#E72410;
} */

.card_h_r text {
  font-size: 24rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #74798c;
  /* opacity: 0.5; */
}

.card_h_r_img {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36rpx;
  height: 36rpx;
}

.card_tag {
  display: flex;
  /* overflow: hidden; */
  width: 580rpx;
}

.card_tag_box text {
  margin-right: 16rpx;
  font-size: 24rpx;
  padding: 2rpx 12rpx 2rpx;
  border-radius: 4rpx;
}

.card_tag_i {
  display: inline-flex;
  padding: 4rpx 12rpx;
  min-width: 72rpx;
  margin-right: 16rpx;
  font-size: 24rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  justify-content: center;
  align-items: center;
  border-radius: 4rpx;
}

.card_c {
  display: flex;
  justify-content: space-around;
  padding: 20rpx 0 20rpx;
}

.card_c_i {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.card_c_is::before {
  position: absolute;
  content: "";
  right: 0;
  top: 50%;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  border-right: 1px solid rgba(238, 238, 238, 0.8);
  width: 0;
  height: 60rpx;
}

.card_c_i .name {
  font-size: 24rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: CENTER;
  color: #9b9eac;
  padding-bottom: 12rpx;
}

.card_c_i .cont {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: CENTER;
  color: #74798c;
}

.card_c_i .contblue {
  color: #1E75DB;
}

/* 那根横线 后面在调 */
.card_ico {
  display: flex;
  justify-content: flex-end;
  flex-wrap: nowrap;
  padding: 16rpx 26rpx 0 0;
  position: relative;
  /* border-top: 1px solid #eee; */
}

.card_ico::before {
  content: " ";
  width: 200%;
  height: 1rpx;
  background: #eee;
  position: absolute;
  top: 0;
  left: -50%;
  transform: scaleY(0.5);
}

.card_ico_i {
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1rpx solid #DEDEDE;
  border-radius: 8rpx;
  margin-right: 40rpx;
  padding: 8rpx 12rpx;
  font-size: 24rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #20263A;
}

.card_ico_i:last-of-type {
  margin-right: 0;
}

.card_ico_i_img {
  width: 32rpx;
  height: 32rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 4rpx;
}
.text-high {
  color: #E72410;
}
/* 开通vip */
.vip {
  margin-top: -324rpx;
}