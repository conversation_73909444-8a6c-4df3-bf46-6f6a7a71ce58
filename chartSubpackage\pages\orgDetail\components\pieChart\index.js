var echarts = require('../../../../ec-canvas/echarts')
const app = getApp();
import { getPx } from '../../../../../utils/formate'

var chart = null;
Component({
  properties: {
    canvasId: {
      type: String,
      observer: function (val) {
        if (val) {
          this.setData({
            domid: val
          })
        }
      }
    }, // 容器ID 必填
    title: String,
    height: String | Number,
    width: String | Number,
    data: {
      type: Array,
      observer: function (val) {
        if (val?.length) {
          setTimeout(() => {
            this.drawChart(val);
          }, 500)
        }
      }
    },
    // 半径
    radius: {
        type: [Number, String, Array],
        value: getPx(130),
    },
    // 圆心位置
    center: {
        type: Array,
        value: () => ['50%', '60%'],
    },
    // 颜色配置
    colors: {
        type: Array,
        value: () => {
        return ['#26C8A7', '#FFB93E', '#4AB8FF'];
      },
    },
    //	文字设置
    labelConfig: {
      type: Object,
      value: () => {
        return {
          padding: [0, 0, 0, 0],
        };
      },
    },
    // 图距离底部位置
    seriesBottom: String
  },
  data: {
    forceUseOldCanvas: false,
    domid: 'mychart-dom-bar',
    ec: {
      lazyLoad: true,
    },
    legendData: [],
  },
  lifetimes: {
    attached() {
      wx.getSystemInfo({
        success: (res) => {
          if (res.platform == 'devtools') {
            this.setData({
              forceUseOldCanvas: true
            })
          }
        },
      })
    },
  },
  methods: {
    drawChart(data) {
      const this_ = this;  
      this.chart1Componnet = this.selectComponent('#mychartpie');
      this.chart1Componnet.init((canvas, width, height, dpr) => {
        chart = echarts.init(canvas, null, {
          width: width,
          height: height,
          devicePixelRatio: dpr
        });
        const option = this_.setOptiondata();
        const legendData = option.series[0].data = data.map((item, index) => {
          const temp = {
              value: item.value,
              name: item.name,
          };
          return temp;
        });
        this.setData({ legendData: legendData.map(item => item.name) });
        chart.clear()
        chart.setOption(option, true);
        let name = legendData[0];//默认选中第一个
        if(data.length) {
          console.log('data', data);
          chart.dispatchAction({
            type: 'highlight',
            seriesIndex: 0,
            name,
          });
          chart.on('mouseover', function (e) {
            if (e.name != name) {
              chart.dispatchAction({
                type: 'downplay',
                seriesIndex: 0,
                name,
              });
            }
          });
          chart.on('mouseout', function (e) {
            name = e.name;
            console.log('e', e);
            chart.dispatchAction({
              type: 'highlight',
              seriesIndex: 0,
              name: e.name,
            });
          });
        }
        chart.on('legendselectchanged', (legendParams) => {
          const { legendData } = this.data;
          const { name: legendName, selected } = legendParams;
          chart.dispatchAction({
            type: 'downplay',
            seriesIndex: 0,
            name,
          });
          if (selected[legendName]) {
            name = legendName;
          } else {
            const defaultName = legendData.find(item => selected[item]);
            chart.dispatchAction({
              type: 'highlight',
              seriesIndex: 0,
              name: defaultName
            });
            name = defaultName;
          }
        });
        return chart;
      });
  },
  setOptiondata() {
      const {seriesBottom } = this.data;
      return {
        color: ['rgba(232, 193, 160, 1)', 'rgba(97, 205, 187, 1)', 'rgba(241, 225, 91, 1)',
        'rgba(232, 168, 56, 1)','rgba(244, 117, 96, 1)', ],
        tooltip: {
          show: false,
          trigger: 'item',
          formatter: (params) => {
            const { data } = params;
            return `${data.name || ''}\n${data.value || ''}`;
          },
        },
        legend: {
          show: true,
          icon: 'circle',
          orient: 'horizontal',
          bottom: 0,
          textStyle: {
            color: '#74798C',
            fontSize:  getPx(24),
            fontWeight: 400,
          },
          formatter: function (name) {
            return name;
          },
          // selectedMode: 'single'
        },
        series: [
          {
          type: 'pie',
          center: ['50%', getPx(240)],
          radius: ['40%', '65%'],
          bottom: seriesBottom,
          avoidLabelOverlap: true,
          itemStyle: {
            borderRadius: 2,
            borderColor: '#fff',
            borderWidth: 1
          },
          label: {
            show: false,
            position: 'center',
            formatter: (item) => {
              return `{name|${item.name}}\n\n{percent|${item.percent}%}`
            },
            align: 'center',
            color: 'red',
            verticalAlign: 'middle',
            // 此处重点，字体大小设置为0
            textStyle: {
              fontSize: '0',
            },
          },
          emphasis: {
            label: {
              show: true,
              rich: {
                name: {
                  fontSize: getPx(28),
                  color: '#74798C',
                  fontWeight: 400,
                },
                percent: {
                  fontSize: getPx(40),
                  color: '#20263A',
                  marginTop: getPx(20),
                  fontWeight: 600,
                },
              },
            },
          },
          labelLine: {
            show: false
          },
          data: [],
        }
      ]
      };
    },
  }
});
