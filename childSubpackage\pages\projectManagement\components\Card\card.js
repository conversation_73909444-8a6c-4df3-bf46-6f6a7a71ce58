// childSubpackage/pages/projectManagement/components/Card/card.js
import { project } from '../../../../../service/api';
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 项目跟进人id
    trackUserId: {
      type: String,
      value: ''
    },
    projectItem: {
      type: Object,
      value: {},
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    operateBtnArr: [{
      name: '联系方式',
      type: 'relation',
      icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>'
    },{
      name: '官网',
      type: 'official',
      icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>'
    },{
      name: '地址',
      type: 'site',
      icon: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>'
    },]
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 删除项目
    delProject() {
      const { projectItem } = this.data;
      this.triggerEvent('del', projectItem)
    },
    // 多选
    handleCheck() {
      const { projectItem } = this.data;
      this.triggerEvent('handleCheck', projectItem.id)
    },
    // 底部功能按钮 
    cardFun({ currentTarget: { dataset: { type, item } } }) {
      if (type === 'official') {
        project.getEntInfo(item.ent_id).then(res => {
          this.copyWebsite(res.official_website);
        });
        return;
      }
      this.triggerEvent('cardFun', {
          type,
          data: item
      });
    },
    // 复制项目官网地址
    copyWebsite(website) {
      if (website) {
        wx.setClipboardData({
          data: website,
          success(res) {
              wx.showToast({
                  title: '复制成功',
                  icon: 'none'
              })
          }
        })
      } else {
        wx.showToast({
          title: '该企业暂无官网',
          icon: 'none'
        })
      }
    }
  }
})
