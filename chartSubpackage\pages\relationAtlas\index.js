import { home } from '../../../service/api';
import request from '../../../service/request';
import { websiteUrl } from '../../../service/config';
import { hasPrivile, handlePayDev } from '../../../utils/route'
import { getHeight } from '../../../utils/height';
import { formatDate } from '../../../utils/formate'
const app = getApp()
Page({
    data: {
        sliderVal: 1, //实际上传的时候这个要+1  为了渲染才需要这样处理（切记）-下转
        related_args: [
            { isEmpty: true }, //只是为了判断是不是为空 点亮关系按钮
            { isEmpty: true },
        ],
        curIndex: -1,// 点击的当前表单 
        isShowInput: true,//是否显示搜索表单
        isFlag: true,
        hisList: [],
        showDialog: false,
        tempFilePaths: '', // 图谱base64
        openSet: false, //重新获取权限,
    },
    onShow() {
      const that = this;
      this.isActivebtn();
      app.showLoading('加载中');
      this.getHist();
      getHeight(that, '.relation', (res) => {
        this.setData({ canvasHeight: res.MaskUsableHeight })
      });
      hasPrivile({
        packageType: true
      }).then((res) => {
        that.setData({
         permission: res,
       })
     });
    },
    // 历史记录列表 
    async getHist() {
        try {
            const res = await home.downloadAtlasHistory()
            // console.log(res)
            if (res.length <= 0) {
                wx.hideLoading()
                return
            };
            this.setData({ hisList: res, })
            wx.hideLoading()
        } catch (err) {
            console.log(err)
            wx.hideLoading()
        }
    },
    // 点击目标主体 
    handle(e) {
        const { type, index } = e.currentTarget.dataset
        const { related_args } = this.data
        // console.log(type)
        switch (type) {
            case 'add':
                if (related_args.length < 10) {
                    related_args.push({ isEmpty: true })
                }
                this.setData({ related_args }, () => {
                    this.isActivebtn()
                })
                break;
            case 'delet':
                related_args.splice(index + 1, 1)
                this.setData({ related_args }, () => {
                    this.isActivebtn()
                })
                break;
            case 'edit':
                //就是编辑
                break;
            default:
                break;
        }
    },
    onSliderChange(e) {
        const val = e.detail.value
        this.setData({
            sliderVal: val,
        })
    },
    addVal(e) { //点击显示添加内容
        const { index } = e.currentTarget.dataset
        this.setData({
            curIndex: index
        })
        // 跳转到添加页面 
        const url = `/companyPackage/pages/mineRelation/search/index?curIndex=${index}`;
        app.route(this, url)
    },
    isActivebtn() { //找关系按钮是否高亮
        let { related_args, isFlag } = this.data
        isFlag = related_args.filter(item => item.isEmpty).length > 0
        this.setData({ isFlag })
    },
    isRepeat(arr) {
        var hash = {};
        for (var i in arr) {
            if (hash[arr[i]]) {
                return true;
            }
            // 不存在该元素，则赋值为true，可以赋任意值，相应的修改if判断条件即可
            hash[arr[i]] = true;
        }
        return false;
    },
    async crateAtlas(){
      const { isFlag, related_args, sliderVal,permission } = this.data
      if (isFlag) return;//没填完
      let params = {
          related_args,
          depth: sliderVal + 1 + ''
      }
      let isrepaeat = this.isRepeat(params.related_args.map(item => item.ent_id))
      if (isrepaeat) {
          app.showToast('请选择不同目标主体!')
          return
      }
      try {
        const res = await home.connectionAtlas(params);
        this.setData({
          showDialog: !res.code 
        });
      } catch(error) {
        if(error.data.code.includes('BusinessPackageErrorCode/OUT_LIMIT_REQUEST_TO_VIP')) {
          this.setData({
            vipVisible: true,
          })
        } else {
          // app.showToast(error.data.code.message)
        }
        wx.hideLoading()
      }
    },
    submit(){
      const this_= this;
      this.setData({ showDialog: false }, () => {
        this_.getHist();
      });
    },
    close(){
      const this_ = this;
      this.setData({ showDialog: false }, () => {
        this_.getHist();
      });
      this.getMoreHistory();
    },
    // 下载图片
    saveImage(e) {
      const { permission } = this.data;
      const that = this;
      const { dataset } = e.currentTarget;
      if(['个人VIP', '团队VIP'].includes(permission)) {
        if (!dataset.url) app.showToast('保存图片失败！');
        wx.downloadFile({
          url: dataset.url,
          success: function(downloadFileRes) {
          wx.getSetting({
            success: (res) => {
              if (!res.authSetting['scope.writePhotosAlbum']) { //没有授权
                wx.authorize({
                  scope: 'scope.writePhotosAlbum',
                  success: () => {
                    wx.saveImageToPhotosAlbum({
                      filePath: downloadFileRes.tempFilePath,
                      success: function (data) {
                        app.showToast('已保存到相册')
                      },
                      fail: function (err) {
                        if (err.errMsg === "saveImageToPhotosAlbum:fail auth deny") {
                          that.setData({ openSet: true })
                        } else {
                          app.showToast("下载失败!")
                        }
                      },
                    })
                  },
                  fail() {
                    // 如果用户拒绝过或没有授权，则再次打开授权窗口
                    //（ps：微信api又改了现在只能通过button才能打开授权设置，以前通过openSet就可打开，现在只有打开授权的button弹窗代码）
                    that.setData({ openSet: true })
                  }
                })
              } else { //有授权
                wx.saveImageToPhotosAlbum({
                  filePath: downloadFileRes.tempFilePath,
                  success: function (data) {
                    app.showToast('已保存到相册')
                  },
                  fail: function (err) {
                    if (err.errMsg === "saveImageToPhotosAlbum:fail auth deny") {
                      that.setData({ openSet: true })
                    } else {
                      app.showToast("下载失败!")
                    }
                  },
                })
              }
            }
          })
          }
        });
      } else {
        this.setData({ vipShow: true })
        app.showToast('您还不是VIP用户!')
      }
    },
    openSetSubmit() {
      this.setData({ openSet: false })
    },
    openSetClose() {
      this.setData({ openSet: false })
    },
    //  去关系图谱页面
    goBackRelation(){
      const url = `/companyPackage/pages/mineRelation/relation`;
      app.route(this, url)
    },
    // 跳转我的导出页面
    getMoreHistory(){
      const url = `/companyPackage/pages/mine/mineExport/mineExport`;
      app.route(this, url)
    },
    //  重新导出
    async recallDownload({currentTarget: {dataset }}){
      const { item } = dataset;
      console.log('e',item );
      await home.recallDownload(item.id);
      this.setData({
        showDialog: true 
      });
    },
    vipPop(val) {
      this.setData({
        vipVisible: val
      })
    },
})