.item-box {
  background-color: #fff;
  margin-top: 20rpx;
}


.item-box .title-box {
  padding: 32rpx 24rpx 22rpx;
  display: flex;
  border-bottom: 1px solid #eee;
}

.item-box .title-box .logo {
  width: 96rpx;
  height: 96rpx;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  overflow: hidden;
  box-shadow: 0px 0px 6rpx #ddd;
  padding: 10rpx;
  flex-shrink: 0;
}

.item-box .title-box .logo image {
  width: 1005;
  height: 100%;
}

.item-box .title-box .name_box {
  flex: 1;
  margin: 0 24rpx 0 20rpx;
}

.item-box .title-box .name_box .name {
  font-weight: 600;
  color: #20263A;
  font-size: 32rpx;
  margin-bottom: 16rpx;
  width: 430rpx;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.topBox {
  width: 100%;
  display: flex;
  justify-content: space-between;
  
}
 .tag-box {
  display: flex;
  padding:0 20rpx 0;
  flex-wrap: wrap;
  /* border: 1px solid red; */
}

.tag-box .tags {
  height: 40rpx;
  line-height: 40rpx;
  padding: 0 12rpx;
  font-size: 24rpx;
  border-radius: 4rpx;
  margin-bottom: 10rpx;
  background: rgba(92, 96, 112, 0.10);
  color: #7F8498;
}

 .tag-box .tags1 {
  color: rgba(38, 200, 167, 1);
  background-color: rgba(38, 200, 167, 0.1);
}


.tag-box .tags2 {
  color: rgba(74, 184, 255, 1);
  background-color: rgba(74, 184, 255, 0.10);
}

 .tag-box .tags3 {
  background: rgb(236, 42, 20,0.1);
  color: rgb(236, 42, 20);
}

 .tag-box .tags4 {
  background: rgba(74, 184, 255, 0.10);
  color: #4AB8FF;
}

 .tag-box .tags5 {
  background: rgba(255, 185, 62, 0.12);
  color: #FFB93E;
}

 .tag-box .tags6 {
  background: rgba(38, 200, 167, 0.10);
  color: #26C8A7;
}

 .tag-box .tags7 {
  background: rgba(156, 133, 219, 0.12);
  color: #9C85DB;
}

 .tag-box .tags8 {
  background: rgba(203, 155, 155, 0.10);
  color: #CB9B9B;
}

 .tag-box .tags9 {
  background: rgba(1, 144, 225, 0.10);
  color: #0190E1;
}

 .tag-box .tags10 {
  background: rgba(0, 173, 211, 0.10);
  color: #00ADD3;
}


 .tag-box .tags:not(:last-child) {
  margin-right: 16rpx;
}

.title-box .time {
  font-size: 24rpx;
  color: #9b9eac;
  width: 140rpx;
  flex-shrink: 0;
}

.dynamic_detail {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  line-height: 46rpx;
  padding: 24rpx;
}