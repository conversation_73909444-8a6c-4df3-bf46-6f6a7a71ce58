import request from '../../../service/request';
import {
  enterpriseUrl
} from '../../../service/config';
import {
  formatDate
} from '../../../utils/formate'
import {
  user,
} from '../../../service/api';
Page({
  data: {
      src:'',
      width:250,//宽度
      height: 250,//高度,
      uploadUrl:"",
  },
  onLoad: function (options) {
      //开始裁剪
      const that=this
      this.setData({
          src:options.src ,
      },()=>{
        //获取到image-cropper对象
        that.cropper = that.selectComponent("#image-cropper");
      });
      wx.showLoading({
          title: '加载中'
      })
  },
  cropperload(e){
      console.log("cropper初始化完成");
  },
  loadimage(e){
      wx.hideLoading();
      //重置图片角度、缩放、位置
      this.cropper.imgReset();
  },
  clickcut(e) {
      //点击裁剪框阅览图片
      wx.previewImage({
          current: e.detail.url, // 当前显示图片的http链接
          urls: [e.detail.url] // 需要预览的图片http链接列表
      })
  },
  //旋转
  rotate(){
    console.log('旋转');
    this.cropper.setAngle(this.cropper.data.angle+=-90)
  },
  getPolicyInfo() {
    let config = {
      url: `${enterpriseUrl}/oss/policy`,
      method: 'GET'
    }
    return request({
      ...config
    }).then(res => {
      return res
    })
  },
  async uploadOSS(uploadUrl){
    let {
      access_id,
      policy,
      signature,
      host
    } = await  this.getPolicyInfo();
    if (access_id) {
      let time = formatDate(new Date().getTime(), 'yyyy-MM-dd')
      let fileName = uploadUrl.substring(uploadUrl.lastIndexOf("/") + 1),
        chars = this.randomString(fileName) + '.png',
        ossFileName = `zs_app/${time}/${chars}`;
        let ossFilePath = `${host}/${ossFileName}`;
        wx.uploadFile({
          url: host,
          filePath: uploadUrl,
          name: 'file',
          formData: {
            key: ossFileName,
            policy: policy,
            OSSAccessKeyId: access_id,
            signature: signature,
            success_action_status: "200"
          },
          success: function (res) {
            user.updatePhoto({head_sculpture_url:ossFilePath}).then(success=>{
              if(success.code==='0000'){
                let data= wx.getStorageSync('userData')
                data.member_data.photo = ossFilePath
                wx.setStorageSync('userData', data)
                wx.navigateBack({url:"/companyPackage/pages/accountSetting/index"})
              }
            })
            
          }
        })
    }
  },
  //生成oss文件名
  randomString(name) {
    var str = '',
      $chars = 'ABCDEFGHJKMNPQRSTWXYZabcdefhijkmnprstwxyz2345678'; // 默认去掉容易混淆的字符oOLl,9gq,Vv,Uu,I1
    let sName = name.substring(0, name.lastIndexOf("."));
    for (let i = 0; i < 5; i++) {
      str += $chars.charAt(Math.floor(Math.random() * $chars.length));
    }
    return sName + '_' + str;
  },
  upload(){
     this.cropper.getImg((obj) => {
      // 这里就是想要截取的图片传给后台的虚拟路径
      this.uploadOSS(obj.url)
    })
  },
  cancel(){
    wx.navigateBack({url:"/companyPackage/pages/accountSetting/index"})
  },
})