var echarts = require('../../../../ec-canvas/echarts')
import { getPx } from '../../../../../utils/formate'

const app = getApp();
var chart = null;
Component({
  properties: {
    canvasId: {
      type: String,
      observer: function (val) {
        if (val) {
          this.setData({
            domid: val
          })
        }
      }
    }, // 容器ID 必填
    height: String | Number,
    width: String | Number,
		// 图表数据
    data: {
      type: Array,
      observer: function (val) {
        if (val?.length) {
          this.drawChart(val);
        }
      }
    },
		unit: {
			type: String,
			value: '',
		},
		// 块颜色集合
    colors: {
      type: Array,
      value: [],
    },
		// 自定义option配置项
		coustomOption: {
			type: Object,
			value: () => {},
		},
		// 自定义series配置项
		series: {
			type: Object,
			value: () => {},
		},
  },
  data: {
    forceUseOldCanvas: false,
    domid: 'mychart-dom-tree',
    ec: {
      lazyLoad: true,
    },
  },
  lifetimes: {
    attached() {
      wx.getSystemInfo({
        success: (res) => {
          if (res.platform == 'devtools') {
            this.setData({
              forceUseOldCanvas: true
            })
          }
        },
      })
    },
  },
  methods: {
    drawChart(data) {
      const { unit, series, coustomOption } = this.data;
      const this_ = this;
      this.chart1Componnet = this.selectComponent('#mychartdomtree');
      this.chart1Componnet.init((canvas, width, height, dpr) => {
        chart = echarts.init(canvas, null, {
          width: width,
          height: height,
          devicePixelRatio: dpr
        });
        const option = {
          ...this_.setOptiondata(),
          ...coustomOption,
        };
        option.series[0] = {
          ...option.series[0],
          ...series,
        };
        chart.clear()
        chart.setOption(option, true);
        return chart;
      })
    },
    setOptiondata() {
      const { colors, data, unit } = this.data;
      const option = {
        color: colors,
        tooltip: {
          position: ['50%', '50%'],
          formatter: function (params) {
            const { data: { value, name }} = params;
            if(name) {
              return name + '\n' + value;
            }
          },
        },
				series: [
					{
						type: 'treemap',
						width: '100%',
            height: '100%',
            roam: false,
            nodeClick: false,
            breadcrumb: false,
            // colorMappingBy: 'index',
						label: {
							color: '#3D4255',
              fontSize: getPx(20),
                overflow: 'truncate',
              formatter: '{b|{b}}\n {d|{c}}',
              rich: {
                b: {
                    fontWeight: 400,
                    wordWrap: 'break-word',
                    fontSize: getPx(24),
                    color: 'rgba(111, 111, 121, 1)',
                },
                d: {
                    fontWeight: 600,
                    color: 'rgba(61, 61, 71, 1)',
                    fontSize: getPx(24),
                    padding:[getPx(20), getPx(20), getPx(20), getPx(0)]
                },
              },
            },
            itemStyle:{
              gapWidth:getPx(2),
              borderColor: "#FFFFFF",
            },
						data: data,
					},
				],
			};
			return option;
    },
  }
});
