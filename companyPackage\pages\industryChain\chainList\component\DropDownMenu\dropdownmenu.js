import {
  params,
  searInputConstant,
  searCheckPopConstant,
  searMultIptConstant,
  renderList,
  checkoutSear,
  handlestructure,
  searMultPopConstant,
  getNameFromPop,
  searRadioConstant,
  searMultiSelectConstant,
  searMultAllConstant,
  dateTagList,
  handleMultiple
} from '../../../../../../components/hunt/mixin.js'
import {
  clone
} from '../../../../../../utils/util'
import {
  chain
} from '../../../../../../service/api';
var huntBehavior = require('../../../../../../components/hunt/common/common')
var behavior = require('../../../../../../template/menuhead/index')
// console.log('renderList', renderList);
const filterAttrs = ['areas', 'trade_types', 'ent_name', 'chain_codes']; // 需要过滤掉属性列表
let searchDataList = renderList.filter(i => {
  if (i.title !== '基础筛选') return i;
}).filter(i => !filterAttrs.includes(i.type))
let filterParams = (function (params) {
  delete params['ent_name'];
  delete params['regionData'];
  delete params['eleseic_data'];
  return params
})(params)

const app = getApp()
Component({
  behaviors: [huntBehavior, behavior],
  properties: {
    heightParams: {
      type: Object,
      observer(val) {
        if (!Object.keys(val || {}).length) return;
        let heightParams = clone(val)
        this.setBackfillData(heightParams, true) //这里要将全国和全部行业拆出来，其它状态填充上去并报错（核心)；
      }
    }
  },
  properties: {
    defaultChainVal: {
      type: String,
      value: '',
      observer() {
        this.setData({
          source_val: true
        })
      }
    }
  },
  data: {
    // 筛选相关
    regionData: [], //选中的地区
    chainData: [],
    // 更多筛选 
    itemList: JSON.parse(JSON.stringify(searchDataList)), // 页面静态数据
    leftList: JSON.parse(JSON.stringify(searchDataList)), // 页面静态数据
    params: filterParams,
    //对上面那个在做一次缓存
    tempParams: {},

    allChainData: [],
    selectChainVal: ''
  },
  observers: {
    regionData: function (list) {
      // console.log(list)
      if (list.length > 0) {
        this.setData({
          district_val: true
        })
      }
    },
    chainData: function (list) {
      if (list.length > 0) {
        this.setData({
          source_val: true
        })
      }
    },
    filter_open: function (filter_open) { //点击上面保留状态
      const {
        tempParams
      } = this.data
      this.setBackfillData(tempParams)
    },
    tempParams: function (obj) { //更多筛选
      this.result(obj)
    },
    params: function (val) {
      this.result(val)
    },
    vipVisible: function (val) {
      // 通过事件传递的方式告诉外面，需要vip弹窗
      console.log('是否需要弹vip弹窗', val)
      this.triggerEvent('vip', true)
    }
  },
  lifetimes: {
    attached: function () {
      // 在组件实例进入页面节点树时执行
      this.getAllChainData();
    },
    detached: function () {
      // 在组件实例被从页面节点树移除时执行
    },
  },
  methods: {
    // 获取产业链数据
    async getAllChainData() {
      let allChainData = await chain.chainAll();
      allChainData = allChainData.map(chain => {
        chain.code = chain.chain_code;
        chain.children = chain.children.map(child => {
          child.code = child.chain_code;
          return child;
        })
        chain.children.unshift({
          name: '全部',
          code: chain.code,
          parent_name: chain.name,
          parent_code: chain.code
        });
        return chain;
      })
      const {
        defaultChainVal
      } = this.data;
      this.setData({
        allChainData,
        selectChainVal: defaultChainVal
      });
    },
    // 地区 
    getRegion(e) {
      let data = JSON.parse(JSON.stringify(e.detail)); //解決checked狀態丟失的情況--拷貝--切記
      // console.log('获取的地区数据', data)
      let {
        regionData,
        district_val
      } = this.data

      if (data[0]?.code == 'all') { //说明是全国的 就清除选中的
        regionData = []
      } else {
        regionData = data
      }
      district_val = regionData.length > 0
      this.setData({
        regionData,
        district_val
      }, () => this.backAll());
    },
    // 选中产业链
    handleSelectChain({
      detail
    }) {
      this.setData({
        chainData: detail
      }, () => this.backAll());
      this.closeHyFilter();
    },
    closeRegion(e) {
      this.closeHyFilter()
    },
    // 组装回填数据 --外部直接调用子组件方法---这里还要改成之前的
    setBackfillData(tempObj = {}, isHeight) {
      // console.log(tempObj)
      this.resetCondition(isHeight)
      let {
        itemList,
        params,
        minCapital,
        maxCapital,
        capitalActive,
        socialActive,
        socialminPeson,
        socialmaxPeson,
        minDate,
        maxDate,
        dateActive,
        regionData,
        chainData
      } = this.data;
      params = Object.assign(params, tempObj);
      if (isHeight) { //表示从高级筛选过来的 --地区和行业单独拿出来
        regionData = params?.regionData || [];
        chainData = params?.eleseic_data || [];
        // console.log(regionData,chainData)
        delete params['regionData']
        delete params['eleseic_data']
        // console.log('高级筛选参数', params)
      }
      itemList = itemList.map(item => {
        for (let key in params) {
          if (item.type === key) {
            let tagId = '';
            // 根据数据类型设置对应的tagId
            switch (item.type) {
              case 'register_capital': //  注册资本
                let strs = params[key].length ? params[key][0].start + '$' + params[key][0].end : '';
                let tagIdArr = ['0$100', '100$200', '500$1000', '1000$5000', '5000$']; //这里后续要弄成三元 还有其它形式
                // 如果返回的注册资本的数据不是tag存在的数据则设置给input组件
                if (!tagIdArr.includes(strs)) {
                  minCapital = params[key].length ? params[key][0].start : '';
                  maxCapital = params[key].length ? params[key][0].end : '';

                  // 只要input存在数据，则设置为活动状态
                  if (minCapital || maxCapital) capitalActive = true;
                  tagId = strs;
                } else {
                  tagId = params[key]
                }
                // 
                break;
              case 'super_dimension_social_num': //  注册资本
                let strss = params[key].length ? params[key][0].start + '$' + params[key][0].end : '';
                let tagIdArrs = ['0$49', '50$99', '100$499', '500$999', '1000$4999', '5000$']; //这里后续要弄成三元 还有其它形式
                // 如果返回的注册资本的数据不是tag存在的数据则设置给input组件 
                if (!tagIdArrs.includes(strss)) {
                  socialminPeson = params[key].length ? params[key][0].start : '';
                  socialmaxPeson = params[key].length ? params[key][0].end : '';

                  // 只要input存在数据，则设置为活动状态
                  if (socialminPeson || socialmaxPeson) socialActive = true;
                  tagId = strss;
                } else {
                  tagId = params[key]
                }
                // 
                break;

              case 'register_time': //  注册时间
                let str = params[key].length ? params[key][0].start + '$' + params[key][0].end : '';
                // 如果返回的注册时间的数据不是tag存在的数据则设置给input组件
                if (!dateTagList.includes(str)) {
                  minDate = params[key].length ? params[key][0].start : '';
                  maxDate = params[key].length ? params[key][0].end : '';

                  // 只要input存在数据，则设置为活动状态
                  if (minDate || maxDate) dateActive = true;
                  tagId = str;
                } else {
                  tagId = params[key]
                }
                break;
                // case 'ent_scale': //企业规模
                //     tagId = params[key];
                // case 'capital_event': // --这个目前没有，不知道渲染起没有
                //     tagId = params[key];
                //     break;
                // case 'shit_type':
                //     tagId = params[key];
                //     break;
                // case 'ent_status':
                //     tagId = params[key];
                //     break;
                // case 'contact_style': // 手机号码
                //     tagId = params[key];
                //     break;
                // case 'contacts_style': // 联系电话
                //     tagId = params[key];
                //     break;
                // case 'technology_types': // 联系电话
                //     tagId = params[key];
                //     break;
              default:
                tagId = params[key];
                break;
            }
            // 设置地区-行业-企业类型，企业许可
            if (Object.keys(searMultPopConstant).includes(key)) {
              let obj = params[searMultPopConstant[key]['data']]
              if (obj.length < 0) return
              item.content = getNameFromPop(obj)
            } else if (searRadioConstant['list'].includes(key) || searMultiSelectConstant['list'].includes(key)) {
              // console.log(tagId)
              // 通过tagId比对判断是否选中
              item.list = item.list.map(tag => {
                const isArray = Array.isArray(tagId); // 判断是否是联系方式的回填数据
                if (isArray) {
                  for (let number of tagId) {
                    if (tag.id === number) {
                      tag.active = true;
                    }
                  }
                } else {
                  if (tag.id === tagId) {
                    tag.active = true;
                  }
                }
                return tag;
              })
            } else if (searMultAllConstant['list'].includes(key)) {
              item.list = item.list.map(tag => {
                Array.isArray(tagId) && tagId.length > 0 && tagId.forEach((itm, idx) => {
                  let ids = params[key][idx].start + '$' + params[key][idx].end;
                  if (tag.id == ids) tag.active = true;
                })
                return tag
              })
            }
          }
        }
        return item;
      })
      // console.log(params)
      this.setData({
        params,
        itemList,
        minCapital,
        maxCapital,
        capitalActive,
        socialActive,
        socialminPeson,
        socialmaxPeson,
        minDate,
        maxDate,
        dateActive,
        regionData,
        chainData,
        tempParams: JSON.parse(JSON.stringify(params))
      })
    },
    // 重置筛选条件
    resetCondition(isHeight) {
      //isHeight表示从高级搜索过来的 --需要重新搞 
      let obj = clone(params)
      delete obj['regionData']
      delete obj['eleseic_data']
      let data = {
        itemList: JSON.parse(JSON.stringify(searchDataList)), //重置状态
        params: obj, //这里的params值为空数组那部分怎麽拷貝都有問題
        minCapital: '',
        maxCapital: '',
        capitalActive: false,
        minDate: '',
        maxDate: '',
        dateActive: false,
        socialActive: false,
        socialminPeson: "",
        socialmaxPeson: "",
      }
      if (isHeight) { //将地区和行业重置为空数组
        data.regionData = [],
          data.chainData = []
        data.ent_name = ''
      }
      this.setData(data, () => {
        !isHeight && this.result(this.data.tempParams)
      })
    },
    clearSear() { //这里不能重置掉全国和全部行业
      this.resetCondition(false)
    },
    result(val) { //自定义需要单独调用，弹窗那种也需要调用
      let isHeight = false //是否有选中的 外面设置高亮要用
      let paramsData = clone(val) ? clone(val) : clone(this.data.params);
      // 排除地区，交通，企业名字因为这里设置高亮
      delete paramsData['areas']
      delete paramsData['trade_types']
      delete paramsData['ent_name']
      Object.keys(paramsData).some(keys => { //这里如果遇到以前的数据可能会报错，所以以前的数据后端要清除没法兼容
        // 这种有自定义需要单独处理
        if (searCheckPopConstant['list'].includes(keys) || searMultIptConstant['list'].includes(keys)) {
          if (paramsData[keys].length > 0 && (paramsData[keys][0]?.start || paramsData[keys][0]?.end)) {
            isHeight = true
            return true
          } else if (searInputConstant['list'].includes(keys)) {
            if (paramsData[keys].trim().length > 0) {
              isHeight = true
              return true
            }
          } else {
            isHeight = false
          }
        } else if (paramsData[keys].length > 0) {
          isHeight = true
          return true
        }
      })
      //把结果抛出去  
      // console.log(isHeight)
      this.setData({
        filter_val: isHeight
      })
      // this.triggerEvent('submit', {
      //   isHeight,
      //   paramsData: clone(paramsData)
      // })
      return isHeight
    },
    // 更多筛选确定
    search() {
      let {
        itemList,
        minCapital,
        maxCapital,
        minDate,
        maxDate,
        socialActive,
        socialminPeson,
        socialmaxPeson,
        curfilterData,
        filter_val
      } = this.data; // 当前页参数
      let params = clone(this.data.params);
      if (filter_val && !checkoutSear(params)) {
        return
      }
      for (let item of itemList) {

        // 注册资本
        item.type === 'register_capital' && this.setParmas(item, params, minCapital, maxCapital);
        // 参保人数
        item.type === 'super_dimension_social_num' && this.setParmas(item, params, socialminPeson, socialmaxPeson);

        // 注册时间
        item.type === 'register_time' && this.setParmas(item, params, minDate, maxDate);
      }
      // 开始和结束都没选齐的情况
      searMultIptConstant['list'].forEach(i => {
        if (params[i][0] && params[i][0].start == '' && params[i][0].end == '') {
          params[i] = []
        }
      })
      searCheckPopConstant['list'].forEach(i => {
        if (params[i][0] && params[i][0].start == '' && params[i][0].end == '') {
          params[i] = []
        }
      })
      params['ent_type'] = params.enttype_data?.length > 0 ? handleMultiple(params.enttype_data).filter(i => i.status === 'checked').map(i => i.code) : []
      params['ent_cert'] = params.all_cert_data?.length > 0 ? handleMultiple(params.all_cert_data).filter(i => i.status === 'checked').map(i => i.code) : []
      curfilterData = Object.assign({}, params);

      let tempParams = JSON.parse(JSON.stringify(curfilterData)) //这里的tempParams和高级搜索哪里不一样 

      this.setData({
        curfilterData,
        filter_val,
        params,
        tempParams
      }, () => this.backAll(true))
    },
    // 总的返回
    backAll(type = false) {
      const {
        regionData,
        chainData,
        curfilterData,
      } = this.data
      let arr = [],
        arr1 = [],
        name1 = '',
        name2 = '';
      if (regionData.length > 0) {
        arr = handleMultiple(regionData).filter(item => item.status === 'checked').map(item => item.code);
        let str = getNameFromPop(regionData)
        name1 = str.length >= 4 ? str.slice(0, 4) + '...' : str;
      } else {
        name1 = '全国'
      }
      if (chainData.length > 0) {
        arr1 = chainData;
        const str = arr1[0].name === '全部' ? arr1[0].parent_name : arr1[0].name;
        name2 = str.length >= 4 ? str.slice(0, 4) + '...' : str;
        // name2 = chainData.sort((a, b) => (a.level - b.level)).map(i => i.name).join(',').slice(0, 4) + '...'
      } else {
        name2 = ''
      }
      let endFilterData = handlestructure(curfilterData)
      delete endFilterData['enttype_data']
      delete endFilterData['all_cert_data']
      const requestData = {
        name1,
        name2,
        ...endFilterData, //更多筛选
        chain_codes: arr1, //行业
        areas: arr, //地区,
        isFilter: type
      }
      this.triggerEvent('submit', requestData)
      this.closeHyFilter();
    }
  },
})

/*
  判断当前是否选值：第一种就是在选择之后就要将__val设置为true 第二种就是反向填充的时候需要判断
*/