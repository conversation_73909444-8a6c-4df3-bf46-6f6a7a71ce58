.layout-box {
  background-color: #fff;
  border-radius: 8rpx;
}

.layout-box .title {
  display: flex;
  height: 92rpx;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 84rpx;
  color: #20263A;
  border-bottom: 1rpx solid #eee;
  padding: 0 20rpx;
  z-index: 2;
}

.layout-box .title .left {
  display: flex;
  align-items: center;
  line-height: 40rpx;
}

.layout-box .title .left>view:first-child {
  font-size: 32rpx;
  font-weight: 600;
  color: #20263A;
  flex: 1;
}

.layout-box .content {
  display: flex;
  align-items: center;
  z-index: 1;
}

.layout-box .content .box {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  background-color: #076EE4;
}

.layout-box .content .box .title {
  width: 100%;
  text-align: center;
  font-size: 28rpx;
  font-weight: 600;
  margin-top: 56rpx;
  margin-bottom: 40rpx;
  color: #20263A;
}