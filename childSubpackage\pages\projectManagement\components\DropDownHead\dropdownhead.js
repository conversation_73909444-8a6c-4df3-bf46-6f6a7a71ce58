Component({
  /**
   * 组件的属性列表
   */
  properties: {
    headGroup: {
      type: Array,
      value: []
    },
    hasCheckbox: {
      type: Boolean,
      value: false
    },
    checked: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    icons: [
      'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/menu-down.png', 
      'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/menu-up.png',
      'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/menu-down-a.png', 
    ]
  },
  /**
   * 组件的方法列表
   */
  methods: {
    // 全选
    handleCheckAll() {
      const { checked } = this.data;
      this.triggerEvent('check', !checked)
    },
    handleClick({ currentTarget: { dataset: { item, index } } }) {
      const { code, showPop } = item;
      this.triggerEvent('select', { code, showPop: !showPop, index })
    }
  }
})
