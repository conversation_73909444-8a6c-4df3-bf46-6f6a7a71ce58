<view>
  <block wx:for="{{list}}" wx:key="index">
    <view class="item-box">
      <view class="title-box">
        <view class="logo">
          <image src="{{item.ent_logo ==''?'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/default.png':item.ent_logo }}" binderror="errorFunction" data-index="{{index}}"></image>
        </view>
        <view class="name_box">
          <view class="name">{{item.entname}}</view>
          <view class="tag-box">
            <view class="tags {{'tags'+item.risk_level}}">{{item.risk_level_name}}</view>
            <view class="tags tags5">{{item.risk_type_name}}</view>
          </view>
        </view>
        <view class="time">{{time || item.created}}</view>
      </view>
      <!-- 这个具体渲染还要根据后面接口定   -->
      <!-- 设计图：案号，案由，裁定日期 -->
      <view class="dynamic_detail">
        <block wx:for="{{item.detail}}" wx:key="index" wx:for-index="index" wx:for-item="itm">
          <view class="detail-items">
            <view class="label">{{itm.filed}}</view>
            <view class="text">{{itm.value || '--'}}</view>
          </view>
        </block>
      </view>
    </view>
  </block>
</view>