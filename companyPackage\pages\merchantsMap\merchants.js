import {
  setTagColor,
  preventActive,
  getSetting
} from '../../../utils/util';
import {
  map,
  common
} from '../../../service/api';
import {
  collect
} from '../../../utils/mixin/collect';
import {
  getHeight
} from '../../../utils/height';
var mapMixin = require('./mapMixin.js');
const app = getApp();
Page({
  behaviors: [mapMixin()],
  data: {
    scrollHeight: 'auto',
    statusBarHeight: 'auto',
    y: '668rpx',
    params: {
      shape: 'CIRCLE',
      radius: 1000,
      page_index: 1,
      page_size: 10
    },
    searchVal: '',
    count: 0,
    chainScroll: false,
    startY: 0,
    hideClass: false,
    hasData: true, // 判断接口是否还有数据返回
    requestData: [],
    // 地图相关 - 高德地图key
    key: 'd9a2bead78cad70c5e01899e3f5d5381',
    scale: 12, //3-20
    latitude: '',
    longitude: '',
    markers: [],
    circles: [],
    isLocation: true, //是否展示该页面 ---后续考虑是给一个默认值
    sliderVal: 4,

    // 弹窗相关
    popType: '',
    showVisible: false, //是否显示弹窗
    title: '',
    content: '',
    cancelBtnText: '取消',
    confirmBtnText: '删除',
    cnacelEnt: '', //缓存取消收藏企业id
    popIndex: 0, //缓存取消收藏企业下标
    // 联系方式
    showContact: false,
    contactList: [], //联系方式列表
    activeEntId: '', // 当前点击企业的id
    // 地址
    showAddress: false,
    addmarkers: [],
    locationTxt: '',
    locationMap: {},
    location: {
      lat: '',
      lon: ''
    },
    isAn: false,
    isLogin: app.isLogin(),
    emptyBoxHeight: '' // 空容器高度
  },
  onLoad: function () {
    this.getEmptyBoxHeight();
    const info = wx.getSystemInfoSync();
    if (info.platform === 'android') {
      this.setData({
        isAn: true
      });
    }
  },
  onShow: function () {
    // 1.初始化
    this._initializeMap();
    // 2.获取当前原始中心经纬度 如果本地有地图上渲染本地的 而不渲染原始地图
    getSetting(this._initializeLocation());
    this.setData({
      isLogin: app.isLogin()
    });
  },
  goMap() {
    const {
      locationMap
    } = this.data;
    wx.openLocation(locationMap);
  },
  onCloseAddress() {
    this.setData({
      showAddress: false
    });
  },
  // 动态获取页面高度
  scrollH() {
    var that = this;
    wx.getSystemInfo({
      success: res => {
        let statusBarHeight = wx.getSystemInfoSync().statusBarHeight;
        let screeHeight = wx.getSystemInfoSync().windowHeight;
        //   通过query获取其余盒子的高度
        let query = wx.createSelectorQuery().in(that);
        query.select('.merch').boundingClientRect();
        query.select('.merch-menu').boundingClientRect();
        // 通过query.exec返回数组
        query.exec(res => {
          let h1 = res[0].height;
          let h2 = res[1].height;
          // 浏览历史的滚动高度
          let scrollHeight = screeHeight - h1 - h2;
          that.setData({
            scrollHeight: scrollHeight,
            statusBarHeight: statusBarHeight
          });
        });
      }
    });
  },
  searchContent(e) {
    let keyWord = e.detail.value;
    // console.log("确定之后", keyWord);
    this.setData({
      searchVal: keyWord,
      hasData: true,
      'params.ent_name': keyWord,
      y: '0',
      hideClass: true,
      'params.page_index': 1,
      requestData: []
    });
    this.getHot();
    this.getList();
  },
  onIndustryChange(e) {
    let {
      params
    } = this.data;
    params.trade_types = e.detail;
    params.page_index = 1;
    this.setData({
      hasData: true,
      'params.page_index': 1,
      y: '0',
      hideClass: true,
      requestData: []
    });
    this.getHot();
    this.getList(false);
  },
  onSliderChange(e) {
    // console.log("父页面滑块改变", e);
    const val = e.detail.slider;
    // 圆变大的时候 -- 地图变小一点
    let scall = this.data.scale;
    if (val < 4) {
      scall = 12;
    } else if (val <= 7) {
      scall = 11;
    } else {
      scall = 10;
    }

    // 更新圆圈半径（使用mapMixin的方法）
    this.updateCircleRadius(val);

    // 这里应该还要发请求 获取数据 渲染好多家相关企业
    this.setData({
      scale: scall,
      'params.radius': val * 1000,
      'params.page_index': 1,
      y: '668rpx',
      hideClass: false,
      hasData: true,
      requestData: []
    });
    this.getHot();
    this.getList(false);
  },
  onSearchChange(e) {
    let {
      params
    } = this.data;
    let paramsNew = e.detail;
    // console.log("paramsNew", paramsNew);
    // 这里不能在用Object.assign 需要将params里面的参数拿出来 因为paramsNew的关系
    let {
      grids = [],
        page_index,
        page_size,
        radius,
        shape,
        trade_types = [],
        ent_name = ''
    } = params;
    params = {
      grids,
      page_index,
      page_size,
      radius,
      shape,
      trade_types,
      ent_name,
      ...paramsNew
    };
    params.page_index = 1;
    this.setData({
      params,
      y: '0',
      hideClass: true,
      requestData: []
    });
    this.getHot();
    this.getList(false);
  },

  async getList(bl = true) {
    wx.showLoading();
    let {
      params,
      hasData,
      requestData,
      searchVal
    } = this.data;
    hasData = true;
    // console.log("getList", params);
    let res = await map.mapList(params);
    res.items = res.items.map(item => {
      item.tags = setTagColor(item.tags);
      item.distance = this.distance(item.location.lat, item.location.lon);
      return item;
    });
    res.items.forEach(item => {
      item.chainArr = this.arrfiy(searchVal, item.ent_name);
      item.tags = item.tags.splice(0, 3);
    });
    if (res.items.length < params.page_size || res.count == params.page_size)
      hasData = false;
    // console.log("企业列表结果", res);
    this.setData({
      // count: res.count,
      searchVal,
      hasData,
      requestData: bl ? requestData.concat(res.items) : res.items
    });
    wx.hideLoading();
  },
  arrfiy(key, chain) {
    return chain.replace(new RegExp(`${key}`, 'g'), `%%${key}%%`).split('%%');
  },
  Rad: function (d) {
    //根据经纬度判断距离
    return (d * Math.PI) / 180.0;
  },
  distance: function (lat2, lng2) {
    let {
      latitude: lat1,
      longitude: lng1
    } = this.data;
    var radLat1 = this.Rad(lat1);
    var radLat2 = this.Rad(lat2);
    var a = radLat1 - radLat2;
    var b = this.Rad(lng1) - this.Rad(lng2);
    var s =
      2 *
      Math.asin(
        Math.sqrt(
          Math.pow(Math.sin(a / 2), 2) +
          Math.cos(radLat1) * Math.cos(radLat2) * Math.pow(Math.sin(b / 2), 2)
        )
      );
    s = s * 6378.137;
    s = Math.round(s * 10000) / 10000;
    return s;
  },
  // 开始滑动
  touchstart(e) {
    let {
      chainScroll
    } = this.data;
    this.setData({
      // mainHeight:"calc( 100vh - 622rpx)",
      chainScroll: false
    });
    // console.log("滑动开始", e.changedTouches[0])
    this.setData({
      startY: e.changedTouches[0].pageY
    });
  },
  // 结束滑动
  touchend(e) {
    let {
      startY,
      chainScroll,
      y
    } = this.data;
    if (chainScroll) {
      // console.log("产业链内滚动触发，外层无动作");
      this.setData({
        y
      });
    } else {
      // console.log("滑动结束", e.changedTouches[0])
      let endY = e.changedTouches[0].pageY;
      console.log('endY < startY', endY, startY);
      if (endY < startY) {
        this.setData({
          y: '0',
          hideClass: true,
          emptyBoxHeight: '100%'
        });
      } else if (endY > startY) {
        this.setData({
          y: '668rpx',
          hideClass: false
        });
        this.getEmptyBoxHeight();
      }
    }
  },
  // 获取数据为空时空容器的高度
  getEmptyBoxHeight() {
    getHeight(
      this,
      ['.merch', '.br_line', '.merch-menu', '.move_box'],
      data => {
        const {
          screeHeight,
          res
        } = data;
        const h1 = res[0]?.height || 0;
        const h2 = res[1]?.height || 0;
        const h3 = res[2]?.height || 0;
        const h4 = res[3]?.height || 0;
        this.setData({
          emptyBoxHeight: screeHeight - h1 - h2 - h3 - h4 + 'px'
        });
      }
    );
  },
  // 产业链滚动
  bindscroll() {
    // console.log("传递事件-产业链");
    this.setData({
      chainScroll: true
    });
  },
  loadMore() {
    // console.log("触发loadMore");
    let {
      params,
      hasData
    } = this.data;
    if (!hasData) {
      wx.showToast({
        title: '已经到底啦',
        icon: 'error'
      });
      return;
    }
    params.page_index += 1;
    this.setData({
      params
    });
    this.getList();
  },
  // 跳转到地图搜索页面
  goSearch() {
    app.route(this, '/companyPackage/pages/mapSearchList/index');
    // 弹窗回调
  },
  // 卡片点击回调
  async onCard(data) {
    let that = this;
    preventActive(this, async () => {
      // 地址  "site"  联系方式 "relation" 官网 "official" 收藏 "collect" 四个字段对呀四个方法逻辑-具体见设计图
      const type = data.detail.type;
      let comDetail = data.detail.data;
      // 处理收藏
      if (type == 'collect') {
        comDetail.tags = comDetail.tags.map(tag => tag.tagName);
        collect(that, comDetail, 'requestData');
      } else if (type == 'relation') {
        this.setData({
          activeEntId: comDetail.ent_id,
          showContact: true
        });
      } else if (type === 'site') {
        this.setData({
          location: {
            lat: +comDetail.location.lat,
            lon: +comDetail.location.lon
          },
          locationTxt: comDetail.register_address,
          addmarkers: [{
            id: 1,
            latitude: +comDetail.location.lat,
            longitude: +comDetail.location.lon,
            iconPath: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/marker.png',
            width: 20,
            height: 20
          }],
          showAddress: true,
          locationMap: {
            latitude: +comDetail.location.lat, //维度
            longitude: +comDetail.location.lon, //经度
            name: comDetail.register_address, //目的地定位名称
            scale: 15, //缩放比例
            address: comDetail.register_address //导航详细地址
          }
        });
      }
    });
  },
  onCloseContact() {
    this.setData({
      showContact: false
    });
  },
  onCloseAddress() {
    this.setData({
      showAddress: false
    });
  },
  makeCall(e) {
    const item = e.target.dataset['item'] || e.currentTarget.dataset['item'];
    wx.makePhoneCall({
      phoneNumber: item.contact_data
    });
  },
  vipPop(val) {
    this.setData({
      vipVisible: val
    });
  },
});