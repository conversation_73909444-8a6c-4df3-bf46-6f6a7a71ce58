
.item_box {
  margin: 40rpx auto 0 auto;
}
.list_wrapper {
  margin-top: 24rpx;
}
.top_box {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.list {
  width: 100%;
  height: 54rpx;
  display:flex;
  flex-wrap: wrap;
  margin-bottom: 26rpx;
}

.list .name {
  font-size: 24rpx;
  font-weight: 400;
  color: #3D4255;
}
.list .unit {
  font-size: 24rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
}
.list .content {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8rpx;

}
.list .process {
  position: relative;
  width: 100%;
  height: 6px;
  background: #EEEEEE;
  border-radius: 4rpx;
}
.list .process .item {
  height: 12rpx;
  background: linear-gradient(270deg, #4AB8FF 0%, #B1E1FF 100%);
  border-radius: 4rpx;
}
