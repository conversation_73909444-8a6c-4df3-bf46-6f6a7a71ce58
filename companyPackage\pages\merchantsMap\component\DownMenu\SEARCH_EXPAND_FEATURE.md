# 搜索框展开功能实现

## 功能描述

实现了一个响应式的搜索框展开效果：
- **默认状态**：左边显示下拉框，右边显示140rpx宽度的搜索框
- **展开状态**：点击搜索框时，下拉框隐藏，搜索框占满全部宽度
- **收起状态**：点击"收起"按钮或失去焦点时，恢复默认状态

## 实现效果

### 默认状态（收起）
```
┌─────────────────────┬──────────┐
│   下拉框菜单区域     │ 搜索框   │
│  (地区/距离/筛选)   │ (140rpx) │
└─────────────────────┴──────────┘
```

### 展开状态
```
┌─────────────────────────────────┐
│           搜索框 + 收起按钮      │
│          (占满全部宽度)         │
└─────────────────────────────────┘
```

## 代码实现

### 1. WXML结构修改

**修改前：**
```xml
<view class="head_nav">
  <view>下拉框模板</view>
  <view>
    <view class="merch">
      <image src="search.png"></image>
      <input class="merch-input" placeholder="附近企业中搜索" />
      <text>收起</text>
    </view>
  </view>
</view>
```

**修改后：**
```xml
<view class="head_nav">
  <!-- 左侧下拉框区域 -->
  <view class="dropdown-section {{searchExpanded ? 'hidden' : ''}}" wx:if="{{!searchExpanded}}">
    <template is='menu-head' data="{{...}}"></template>
  </view>
  
  <!-- 右侧搜索框区域 -->
  <view class="search-section {{searchExpanded ? 'expanded' : 'collapsed'}}">
    <view class="search-container">
      <image src="search.png" class="search-icon"></image>
      <input 
        class="search-input" 
        placeholder="附近企业中搜索" 
        bindfocus="onSearchFocus"
        bindblur="onSearchBlur"
        value="{{searchVal}}" 
      />
      <text class="collapse-btn {{searchExpanded ? 'show' : 'hide'}}" bindtap="collapseSearch">收起</text>
    </view>
  </view>
</view>
```

### 2. WXSS样式实现

#### 核心样式类：

```css
/* 头部导航容器 */
.head_nav {
  display: flex;
  align-items: center;
  padding: 0 24rpx;
  transition: all 0.3s ease;
}

/* 下拉框区域 */
.dropdown-section {
  flex: 1;
  transition: all 0.3s ease;
  opacity: 1;
  transform: translateX(0);
}

.dropdown-section.hidden {
  opacity: 0;
  transform: translateX(-20rpx);
  pointer-events: none;
}

/* 搜索框区域 */
.search-section.collapsed {
  width: 140rpx;          /* 默认宽度 */
  flex-shrink: 0;
}

.search-section.expanded {
  flex: 1;                /* 展开时占满剩余空间 */
  width: 100%;
}

/* 搜索容器 */
.search-container {
  height: 72rpx;
  background: #eeeeee;
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  transition: all 0.3s ease;
}

/* 收起按钮 */
.collapse-btn.hide {
  opacity: 0;
  pointer-events: none;
  transform: translateX(20rpx);
}

.collapse-btn.show {
  opacity: 1;
  pointer-events: auto;
  transform: translateX(0);
}
```

### 3. JavaScript逻辑实现

#### 数据状态：
```javascript
data: {
  searchExpanded: false,    // 搜索框是否展开
  searchVal: ''            // 搜索内容
}
```

#### 核心方法：

```javascript
/**
 * 搜索框获得焦点时展开
 */
onSearchFocus() {
  this.setData({
    searchExpanded: true
  });
  
  // 关闭所有下拉菜单
  this.setData({
    district_open: false,
    source_open: false,
    filter_open: false
  });
},

/**
 * 搜索框失去焦点时收起
 */
onSearchBlur() {
  setTimeout(() => {
    if (!this.data.searchVal.trim()) {
      this.setData({
        searchExpanded: false
      });
    }
  }, 150);
},

/**
 * 点击收起按钮
 */
collapseSearch() {
  this.setData({
    searchExpanded: false
  });
  
  if (this.data.searchVal.trim()) {
    this.searchContent();
  }
},

/**
 * 搜索内容处理
 */
searchContent() {
  const searchVal = this.data.searchVal.trim();
  
  // 触发搜索事件给父组件
  this.triggerEvent('searchContent', {
    searchVal: searchVal
  });
  
  this.setData({
    searchExpanded: false
  });
}
```

## 交互流程

### 展开流程：
```
用户点击搜索框
    ↓
onSearchFocus() 触发
    ↓
设置 searchExpanded = true
    ↓
下拉框区域添加 'hidden' 类（淡出+左移）
    ↓
搜索框区域添加 'expanded' 类（占满宽度）
    ↓
收起按钮显示（淡入）
    ↓
关闭所有下拉菜单
```

### 收起流程：
```
用户点击收起按钮 或 搜索框失去焦点
    ↓
collapseSearch() 或 onSearchBlur() 触发
    ↓
设置 searchExpanded = false
    ↓
搜索框区域恢复 'collapsed' 类（140rpx宽度）
    ↓
下拉框区域移除 'hidden' 类（淡入+归位）
    ↓
收起按钮隐藏（淡出）
    ↓
如有搜索内容则触发搜索事件
```

## 动画效果

### CSS过渡动画：
- **持续时间**：0.3秒
- **缓动函数**：ease
- **动画属性**：
  - `opacity`：透明度变化
  - `transform`：位移变化
  - `width/flex`：宽度变化

### 动画细节：
1. **下拉框隐藏**：`opacity: 0` + `translateX(-20rpx)`
2. **搜索框展开**：`width: 140rpx` → `flex: 1`
3. **收起按钮显示**：`opacity: 0` → `opacity: 1` + `translateX(20rpx)` → `translateX(0)`

## 响应式设计

### 小屏幕适配：
```css
@media (max-width: 750rpx) {
  .search-section.collapsed {
    width: 120rpx;        /* 小屏幕时更窄 */
  }
  
  .search-icon {
    width: 32rpx;         /* 图标更小 */
    height: 32rpx;
  }
  
  .collapse-btn {
    font-size: 26rpx;     /* 文字更小 */
  }
}
```

## 用户体验优化

### 1. 防抖处理
- 搜索框失去焦点时延迟150ms收起
- 避免点击收起按钮时的冲突

### 2. 状态保持
- 有搜索内容时失去焦点不自动收起
- 点击收起时自动触发搜索

### 3. 交互反馈
- 平滑的动画过渡
- 清晰的视觉状态变化
- 合理的触发时机

### 4. 功能集成
- 展开搜索框时自动关闭下拉菜单
- 避免界面元素冲突
- 保持整体布局协调

## 兼容性

### 微信小程序版本：
- 支持所有微信小程序版本
- 使用标准CSS3动画
- 兼容不同设备尺寸

### 设备适配：
- iPhone/Android 手机
- 不同屏幕尺寸
- 横屏/竖屏模式

## 扩展性

### 可配置参数：
- 默认搜索框宽度（当前140rpx）
- 动画持续时间（当前0.3s）
- 延迟收起时间（当前150ms）

### 可扩展功能：
- 搜索历史记录
- 搜索建议下拉
- 语音搜索按钮
- 扫码搜索功能

## 总结

这个搜索框展开功能实现了：

1. **✅ 响应式布局**：左右区域灵活切换
2. **✅ 流畅动画**：平滑的展开收起效果
3. **✅ 用户友好**：直观的交互逻辑
4. **✅ 功能完整**：搜索、收起、状态管理
5. **✅ 兼容性好**：支持各种设备和场景

通过CSS Flexbox布局、CSS3动画和JavaScript状态管理的结合，创造了一个现代化的搜索界面体验。
