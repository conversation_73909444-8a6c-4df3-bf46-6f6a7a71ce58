<import src="/template/null/null"></import>
<!-- style="overflow-y: {{ overflow ? 'scroll':'hidden' }};" -->
<view class="allWrap">
  <!-- 搜索框 -->
  <view class="searbox">
    <view class='searchs'>
      <view class="s-input">
        <view class="s-input-img">
          <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/search.png" mode="aspectFit"></image>
        </view>
        <view class="s-input-item">
          <input class="s-input-item-i" type="text" placeholder='请输入应用名称' placeholder-class='placeholder' bindblur='onBlur' value="{{ents_name}}" focus="{{inputShowed}}" bindinput="onInput" confirm-type='search' />
          <view hidden="{{ent_name.length <= 0}}" catchtap="onClear" class="input-clear">
            <view class="clearIcon"></view>
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- 搜索框无内容时 -->
  <view class="zhanwei">
  </view>
  <scroll-view hidden="{{ent_name.length>0}}" class="scroll" scroll-y="{{ overflow ? true: false }}" bindscroll="scroll">
    <!-- 最近使用 -->
    <view class="wrap" wx:if="{{recentUseList.length>0}}">
      <view class="tit">
        最近使用
      </view>
      <view class="cont">
        <block wx:for="{{recentUseList}}" wx:key="index">
          <view class="item" bindtap="clickItm" data-type="history" data-item="{{item}}">
            <image src="{{item.img}}" />
            <view>
              {{item.title}}
            </view>
          </view>
        </block>
      </view>
    </view>
    <!--首页服务 拖拽部分 -->
    <view class="wrap">
      <view class="tit">
        首页服务 <text>长按编辑首页应用 拖动调整顺序</text>
      </view>
      <view class='inner'>
        <movable-area class="cont">
          <block wx:for="{{homePageServiceList}}" wx:key="index">
            <view class='item itemss' id="{{index}}" data-index='{{index}}' bindlongpress='longtap' bindtouchstart='touchs' bindtouchend='touchend' bindtouchmove='touchm' data-item="{{item}}" bindtap="clickItm">
              <image src="{{item.img}}" />
              <view>
                {{item.title}}
              </view>
            </view>
          </block>
          <movable-view x="{{x}}" y="{{y}}" direction="all" damping="{{5000}}" friction="{{1}}" disabled="{{disabled}}">
            <view class='item-move' hidden='{{hidden}}' style="width:{{itemHW.width}}px;height:{{itemHW.height}}px;">
              <view class="copyitem">
                <image src="{{curItem.img}}" />
                <view>
                  {{curItem.title}}
                </view>
              </view>
            </view>
          </movable-view>
        </movable-area>
      </view>
    </view>
    <!-- 综合查询,负面信息,上市公司，知识产权 -->
    <view>
      <block wx:for="{{allList}}" wx:for-index='idx' wx:for-item='itm' wx:key="idx">
        <view class="wrap">
          <view class="tit">
            {{itm.title}}
          </view>
          <view class="cont">
            <block wx:for="{{itm.children}}" wx:key="index">
              <view class="item" data-item="{{item}}" bindtap="clickItm">
                <image src="{{item.img}}" />
                <view>
                  {{item.title}}
                </view>
              </view>
            </block>
          </view>
        </view>
      </block>
    </view>
  </scroll-view>

  <!-- 搜索框有内容时 -->
  <view hidden="{{ent_name.length<=0}}">
    <view class="cont" wx:if="{{hisList.length>0}}">
      <block wx:for="{{hisList}}" wx:key="index">
        <view class="item" bindtap="clickItm" data-item="{{item}}">
          <image src="{{item.img}}" />
          <view class="heightItem">
            <view wx:for="{{ item.heightKey }}" class="text {{item.isHight&&'activetexts'}}" wx:key="index">{{item.text}}</view>
          </view>
        </view>
      </block>
    </view>
    <view class="null" wx:else>
      <template is='null'></template>
    </view>
  </view>
</view>