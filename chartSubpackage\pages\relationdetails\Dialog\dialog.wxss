.pages {
  background-color: #fff;
}
/* 原本样式  */
.fadeIn {
  -webkit-animation: c 0.3s forwards;
  animation: c 0.3s forwards;
}
@-webkit-keyframes c {
  0% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
@keyframes c {
  0% {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
.weui-mask {
  background: rgba(0, 0, 0, 0.6);
}
.weui-mask,
.weui-mask_transparent {
  position: fixed;
  z-index: 1000;
  top: 0;
  right: 0;
  left: 0;
  bottom: 0;
}

.weui-dialog {
  position: fixed;
  z-index: 5000;
  top: 50%;
  left: 32rpx;
  right: 32rpx;
  -webkit-transform: translateY(-50%);
  transform: translateY(-50%);
  background-color: #fff;
  text-align: center;
  border-radius: 24rpx;
  overflow: hidden;
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
  -webkit-flex-direction: column;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  flex-direction: column;
  max-height: 90%;
}
.weui-dialog__hd {
  padding: 64rpx 48rpx 32rpx;
}
.weui-dialog__title {
  font-weight: 700;
  font-size: 34rpx;
  line-height: 1.4;
}
.weui-dialog__bd {
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  padding: 0 48rpx;
  margin-bottom: 64rpx;
  font-size: 34rpx;
  line-height: 1.4;
  word-wrap: break-word;
  -webkit-hyphens: auto;
  hyphens: auto;
  color: rgba(116, 121, 140, 1);
}
.weui-dialog__bd:first-child {
  min-height: 80rpx;
  padding: 64rpx 48rpx 0;
  font-weight: 700;
  color: rgba(0, 0, 0, 0.9);
  -webkit-flex-direction: column;
  -webkit-box-orient: vertical;
  -webkit-box-direction: normal;
  flex-direction: column;
  -webkit-box-pack: center;
  -webkit-justify-content: center;
  justify-content: center;
}
.weui-dialog__bd:first-child,
.weui-dialog__ft {
  display: -webkit-box;
  display: -webkit-flex;
  display: flex;
}
.weui-dialog__ft {
  position: relative;
}
.weui-dialog__ft:after {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  height: 2rpx;
  border-top: 2rpx solid rgba(0, 0, 0, 0.1);
  color: rgba(0, 0, 0, 0.1);
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleY(0.5);
  transform: scaleY(0.5);
}
.weui-dialog__btn {
  -webkit-box-flex: 1;
  -webkit-flex: 1;
  flex: 1;
  display: block;
  line-height: 1.41176471;
  padding: 32rpx 0;
  font-size: 34rpx;
  color: #576b95;
  font-weight: 700;
  text-decoration: none;
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  position: relative;
  overflow: hidden;
}
.weui-dialog__btn:active {
  background-color: #ececec;
}
.weui-dialog__btn:after {
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  width: 2rpx;
  bottom: 0;
  border-left: 2rpx solid #ececec;
  color: #ececec;
  -webkit-transform-origin: 0 0;
  transform-origin: 0 0;
  -webkit-transform: scaleX(0.5);
  transform: scaleX(0.5);
}
.weui-dialog__btn:first-child:after {
  display: none;
}
.weui-dialog__btn_default {
  color: rgba(0, 0, 0, 0.9);
}
.weui-skin_android .weui-dialog {
  text-align: left;
  box-shadow: 0 12rpx 60rpx 0 rgba(0, 0, 0, 0.1);
}
.weui-skin_android .weui-dialog__title {
  font-size: 44rpx;
  line-height: 1.4;
}
.weui-skin_android .weui-dialog__hd {
  text-align: left;
}
.weui-skin_android .weui-dialog__bd {
  color: hsla(0, 0%, 100%, 0.5);
  text-align: left;
}
.weui-skin_android .weui-dialog__bd:first-child {
  color: hsla(0, 0%, 100%, 0.8);
}
.weui-skin_android .weui-dialog__ft {
  display: block;
  text-align: right;
  line-height: 80rpx;
  min-height: 80rpx;
  padding: 0 48rpx 32rpx;
}
.weui-skin_android .weui-dialog__ft:after {
  display: none;
}
.weui-skin_android .weui-dialog__btn {
  display: inline-block;
  vertical-align: top;
  padding: 0 0.8em;
}
.weui-skin_android .weui-dialog__btn:after {
  display: none;
}
.weui-skin_android .weui-dialog__btn:last-child {
  margin-right: -0.8em;
}
.weui-skin_android .weui-dialog__btn_default {
  color: hsla(0, 0%, 100%, 0.6);
}
@media screen and (min-width: 704rpx) {
  .weui-dialog {
    width: 640rpx;
    margin: 0 auto;
  }
}
/* 后面自己加的 */
.title {
  font-size: 32rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: CENTER;
  color: #20263a;
}
.btn1 {
  font-size: 32rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;

  color: #20263a;
}
.btn2 {
  font-size: 32rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;

  color: #E72410;
}
