.fin-card {
  position: relative;
  width: 100%;
  background: #ffffff;
  padding: 32rpx 0;
  border-bottom: 1rpx solid #F7F7F7;
  display: flex;
}
.fin-card-l {
  width: 96rpx;
  height: 96rpx;
  margin-right: 20rpx;
}

.fin-card-r {
  flex: 1;
}

.card_h {
  display: flex;
  justify-content: space-between;
  flex-wrap: nowrap;
  flex: 1;
  /* 12rpx */
  margin-bottom: 9rpx;
}

.card_h_l {
  /* display: flex; */
  width: 440rpx;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
  flex-wrap: nowrap;
}

.card_h_l .text {
  display: inline;
  font-size: 32rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 600;
  text-align: LEFT;
  color: #20263a;
}

.card_h_l .activetext {
  color:#E72410 !important;
}

.card_h_r {
  display: flex;
  align-items: center;
  justify-content: center;
  width: auto;
  height: 34rpx;
}

.card_h_r text {
  font-size: 24rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #9B9EAC;
}

.card_tag {
  position: relative;
  display: flex;
  overflow: hidden;
  width: 100%;
}

.card_tag_box {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  text-align: justify;
  display: flex;
  align-items: center;
  /* display: -webkit-box; */
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  position: relative;
}

.card_tag_i {
  display:flex;
  padding: 2rpx 10rpx;
  min-width: 72rpx;
  margin-right: 12rpx;
  font-size: 24rpx;
  text-align: center;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  justify-content: center;
  align-items: center;
  border-radius: 4rpx;
}
.card_tag_i_1 {
  color: #26C8A7;
  min-width: 236rpx;
  height: 34rpx;
  border-radius: 4rpx;
  border: 1rpx solid #26C8A7;
}
.card_tag_i_2 {
  width: auto;
  height: 36rpx;
  line-height: 8rpx;
  background: rgba(253,147,49,0.1);
  border-radius: 4rpx;
  color: #FD9331;
}



.card_con {
  padding-top: 20rpx;
  display: flex;
  flex-direction: column;
  /* flex-wrap: ; */
}

.card_con_i {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
}
.card_con_i:first-child {
  margin-bottom: 16rpx;
}

.card_con_i view:nth-of-type(1) {
  width: 140rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  text-align: left;
  color: #74798C;
}

.card_con_i view:nth-of-type(2) {
  flex: 1;
  width: 450rpx;
  height: auto;
  font-size: 28rpx;
  line-height: 40rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #1E75DB;
}
.org_name>text:last-child {
  /* background-color: orange; */
  opacity: 0;
}