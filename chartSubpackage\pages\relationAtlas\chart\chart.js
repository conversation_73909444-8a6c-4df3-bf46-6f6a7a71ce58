// import * as echarts from '../../../ec-canvas/echarts';
var echarts = require('../../../ec-canvas/echarts.js')
import { getPx } from '../../../../utils/formate'
const app = getApp();
//记得在这里全局声明一下
var chart = null;
var pointXPro = 80;
var editRecList = []
Component({
    properties: {
      id: {
        type: String,
        observer: function (val) {
          if (val) {
            this.setData({
              domid: val
            })
          }
        }
      }, // 容器ID 必填
      // 数据
      seriesObj: {
        type: Object,
        value: {},
        observer: function (val) {
          //这里写个订阅事件去监听父组件传过来的值
          if (val && Object.keys(val).length > 0) {
            this.setData({ chainData: val }, () => {
              this.init_Chain(val?.idx ? val.idx : 1)
              // this.init_data(val);
            })
          }
        }
        },
    },
    data: {
        forceUseOldCanvas: false,
        domid: 'mychart2',
        ec: {
          lazyLoad: true,
        },
        tabA: {
            nodes: [],
            edges: [],
            prop_status: false
        },
        nodes: [],
        edges: [],
        myChartId: '',
        chainData: [],//原始数组
    },
    lifetimes: {
      attached() {
        wx.getSystemInfo({
          success: (res) => {
            if (res.platform == 'devtools') {
              this.setData({
                forceUseOldCanvas: true
              })
            }
          },
        })
      },
    },
    methods: {
        // 绘制图表
        init_Chain(idx) { //只取一条
        
            let editRecList = [];
            let lineList = [];
            let { chainData } = this.data
            // 
            chainData.table = chainData.table.filter(item => item.road_no == idx)
            chainData.table.map(item => {
                if (item.ent_id) { editRecList.push(item.ent_id) }
            })


            // chainData.nodes.map((item, index) => {
            //     if (!editRecList.includes(item.ent_id)) {
            //         chainData.nodes.splice(index, 1)
            //     }
            // })
            for (let i = 0; i < chainData.nodes; i++) {
                if (!editRecList.includes(chainData.nodes[i].ent_id)) {
                    chainData.nodes.splice(i, 1);
                    i--;
                }
            }


            // let temolateEdges = [];
            chainData.edges = chainData.edges.filter((item, index) => {
                return editRecList.includes(item.source) && editRecList.includes(item.target)
            })
            // // 如果在线里面没找的的节点也要删除 
            chainData.edges.map((i, idx) => {
                if (i.source || i.target) { lineList.push(i.target); lineList.push(i.source) }
            })
            lineList = Array.from(new Set(lineList))
            chainData.nodes = chainData.nodes.filter((item, index) => lineList.includes(item.ent_id))

            // // console.log(chainData)
            // // 

            this.setData({ chainData }, () => {
                this.init_data(chainData)
            })
        },
        init_data(list) {
            let { tabA } = this.data
            // console.log(list?.nodes)
            tabA.nodes = list?.nodes ? list?.nodes : [];
            tabA.edges = list?.edges ? list?.edges : [];
            this.setData({ tabA }, () => { //将数据tabA 
                this.set_data('first');
            })
        },
        set_data: function (time) {
            const that = this;
            pointXPro = 0
            var best_touzi = {
                color: '#E1643A',
                width: 3,
                type: 'solid'
            };
            var best_other = {
                color: '#50AFD9',
                width: 3,
                type: 'solid'
            };

            var xu_touzi = {
                color: '#E1643A',
                width: 1,
                type: 'dashed'
            };
            var xu_other = {
                color: '#50AFD9',
                width: 1,
                type: 'dashed'
            };
            var hui = {
                color: '#9B9DA7',
                width: 1,
                type: 'dashed'
            };
            let { nodes: vmnodes, edges: vmedges } = this.data;
            vmnodes = that.is_route().nodes;
            vmedges = that.is_route().edges;
            vmedges.forEach(function (edg) {
                if (time) {
                    edg.relation[0] === '投' ? edg.lineStyle = xu_touzi : edg.lineStyle = xu_other;
                } else {
                    if (edg.highlight) {
                        edg.relation[0] === '投' ? edg.lineStyle = best_touzi : edg.lineStyle = best_other;
                    } else {
                        edg.lineStyle = hui;
                        // edg.relation[0]==='投' ? edg.lineStyle =xu_touzi :  edg.lineStyle = xu_other;
                    }
                }
            });
            vmnodes.forEach(function (node) {
                // console.log(node)
                // let tabAPosition = {};
                if (!node.num) { pointXPro += 200 }
                // tabAPosition = { //有num时  position
                //     '0': { x: 0, y: 150 },
                //     '1': { x: 80, y: 150 },
                //     '2': { x: 200, y: 150 },
                //     '3': { x: 300, y: 150 },
                //     '4': { x: 400, y: 150 },
                // };
                let position = {  //定义默认 position
                    x: pointXPro,
                    // y: Math.random() * 600,
                    y: pointXPro + 50
                };
                if (node.num == 2) {
                    position.x = (vmnodes.length - 1) * 100;
                }
                node.fixed = true
                node.x = position.x;
                node.y = position.y;
                node.symbolSize = node.num || node.imgtype === 'com' ? 75 : 75;
                node.name = node.ent_name //文档这样要求
                node.id = node.ent_id
                if (time || node.color === 'more') {
                    if (node.num) {
                        node.itemStyle = {
                            color: '#ffb93e'
                        }
                    } else {
                        if (node.imgtype === 'com') {
                            node.itemStyle = {
                                color: '#3484F6'
                            }
                        } else {
                            node.itemStyle = {
                                color: '#4ab8ff'
                            }
                        }
                    }
                } else {
                    node.itemStyle = {
                        color: '#9B9DA7'
                    }
                }
            });
            this.setData({ nodes: vmnodes, edges: vmedges }, () => {
                this.chain_option()
            })
        },
        is_route: function () {
            const { tabA } = this.data
            let nodes = tabA.nodes;
            let edges = tabA.edges;
            return {
                edges: edges,
                nodes: nodes,
            };
        },
        chain_option: function () {
          const that = this;
          let fontSize = 12;
          let { nodes: vmnodes, edges: vmedges, tabA } = this.data
          this.chart1Componnet = this.selectComponent('#mychart2');
          this.chart1Componnet.init((canvas, width, height, dpr) => {
            chart = echarts.init(canvas, null, {
              width: width,
              height: height,
              devicePixelRatio: dpr
            });
            let edges_hide = vmedges.length && JSON.parse(JSON.stringify(vmedges));
            let _edges = edges_hide && edges_hide.map((item) => {
                if (item.relation.indexOf("投资") > -1) {
                    item.relation = item.relation.split("(")[0];
                }
                return item;
            });
            let edges = [];
            edges = tabA.prop_status ? vmedges : _edges;
            if (!edges || !edges.length) {
                vmnodes = [];
            }
            // 
            let option = {
              backgroundColor: 'white',
              series: [
                {
                  type: "graph",
                  center: ['50%', '50%'],
                  zoom: 0.8,
                  scaleLimit:{
                    min: 0.8,
                    max: 2,
                  },
                  nodeScaleRatio: 0.6,  // 鼠标漫游缩放时节点的相应缩放比例，当设为0时节点不随着鼠标的缩放而缩放
                  layout: "force", //力引导布局
                  symbol: "circle",
                  roam: 'scale', //开启鼠标缩放和平移
                  edgeSymbol: ["circle", "arrow"], // 边两端的标记类型
                  edgeSymbolSize: [4, 8],
                  top: "middle",
                  left: "center",
                  force: {
                    // initLayout:[100, 100],
                    repulsion: 50, //节点之间的距离
                    edgeLength: [10, 50],
                    layoutAnimation: true,
                    gravity: 0.2,
                  },
                  focusNodeAdjacency: false,
                  draggable: true,
                  nodeScaleRatio: 0.7,
                  edgeLabel: {
                    //关系边的公用线条
                    normal: {
                      show: true,
                      textStyle: {
                          fontSize: fontSize,
                          color: "#1E75DB",
                      },
                      formatter: function (x) {
                        return x.data.relation;
                      },
                      position: 'middle',
                    },          
                  },
                  label: {
                    //节点圈里的字体
                    normal: {
                      show: true,
                      position: "inside",
                      fontSize: fontSize,
                      color: "white",
                      align: "center",
                      // verticalAlign:'middle',
                      lineHeight: 12,
                      formatter: function (val) {
                        if (typeof val.name === "object") {
                          return val.name[0] + "(" + val.name[1] + ")";
                        } else {
                          var name =
                            val.name && val.name.length > 15
                            ? val.name.slice(0, 15) + "..."
                            : val.name;
                          var strs = name.split(""); //字符串数组
                          var str = "";
                          for (var i = 0, s; (s = strs[i++]);) {
                            //遍历字符串数组
                            str += s;
                            if (strs.length > 10) {
                              if (!(i % 5)) str += "\n";
                            } else {
                              if (!(i % 4)) str += "\n"; //按需要求余
                            }
                          }
                          return str;
                        }
                      },
                    },
                  },
                  data: vmnodes,
                  links: edges,
                },
              ],
            };

            chart.clear();
            this.setData({ nodes: vmnodes, edges: vmedges })
           
            setTimeout(() => {
              // setOption前隐藏loading事件 
              // chart.hideLoading();
              chart.setOption(option, true);
              that.getImg()
            }, 1000);
            //拖动之后不反弹
            chart.on("mouseup", function (params) {
                // console.log(params)
                let zrX = params.event?.event?.zrX
                let zrY = params.event?.event?.zrY
                let bool = zrX && zrY && params.data && params.data.x && params.data.y
                if (bool === 0) {
                    bool = true
                }
                if (bool) {
                    var option = chart.getOption();
                    option.series[0].data[params.dataIndex].x = params.data?.x; //params.data?.x;
                    option.series[0].data[params.dataIndex].y = params.data?.y;//params.data?.y;
                    option.series[0].data[params.dataIndex].fixed = true;
                    //设置文字大小
                    option.series[0].label.fontSize = fontSize;
                    option.series[0].label.lineHeight = fontSize * 1.25;
                    option.series[0].edgeLabel.textStyle.fontSize = fontSize;
                    chart.setOption(option);
                }
            });

            //设置字体
            let preZoom = 1;
            chart.getZr().on("touchmove", function (e) {
                var option = chart.getOption();
                var zoom = option.series[0].zoom;
                if (zoom !== preZoom) {
                    preZoom = zoom;
                    fontSize = 8 * zoom;
                    option.series[0].label.fontSize = fontSize;
                    option.series[0].label.lineHeight = fontSize * 1.25;
                    option.series[0].edgeLabel.textStyle.fontSize = fontSize;
                }
                chart.setOption(option);
            });
            return chart;
          });
        },
        getImg() {
          let that = this;
          wx.nextTick(() => {
            const ecComponent = that.selectComponent('#mychart2')
            ecComponent.canvasToTempFilePath({
              //安卓机型此处不会成功回调
              success: res => {
                that.triggerEvent('getTempFilePath', res.tempFilePath)
              },
              fail: res => console.log('失败', res)
            });
          })
        }
    }
});