
<view class="item_box">
  <view class="list_wrapper">
    <view wx:for="{{dataList}}" wx:key="index"  data-item="{{item}}" data-index="{{index}}"  class="list">
      <view class="top_box">
        <view class="name">{{item.name}}</view>
        <view class="unit">{{item.value}}家</view>
      </view>
      <view class="content">
        <view class="process">
          <view class="item" style="width: {{ (item.value / maxNum * 100) + '%' }} "></view>
         </view>
      </view>
    </view>
  </view>
</view>
