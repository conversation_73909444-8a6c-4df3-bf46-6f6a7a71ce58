import { setTagColor } from '../../../../../../utils/util.js'
import { home } from "../../../../../../service/api";
const app = getApp()
Component({

  options: {
    addGlobalClass: true,
  },
  /**
   * 组件的属性列表
   */

  properties: {
    souceList: { //传进来的数组 ，渲染 -目前是写死的 --通过setTagColor处理一下标签
      type: <PERSON><PERSON><PERSON>,
      observer(newVal) {
        this.setData({
          souceData: this.onHandlerKewWord(newVal, "ent_name"),
        })
      }
    },
    keyword: {
      type: String,
      value: ''
    },
    hasData: {
      type: Boolean,
      value: true
    },
    pageSize:{
      type: Number,
      value: 10
    },
    permission: {
      type: String,
      value: ''
    },
    height: {
      type: Number,
      value: 0
    },
    isLogin: {
      type: Boolean,
      value: false
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    souceData: [],
  },

  /**
   * 组件的方法列表
   */
  methods: {
    login() {
      const { keyword } = this.data;
      const url = `/companyPackage/pages/industryChain/chainListNew/chainList?key_word=${keyword}`;
      app.route(this, `/pages/login/login?url=${url}`);
    },
    vipPop(val) {
      this.triggerEvent('vipPop', val)
    },
    // 关键词处理
    onHandlerKewWord(list, key) {
      const { keyword } = this.data;
      return list.reduce((result, items) => {
        const _tagName = items[key].replace(new RegExp(`(${keyword})`), "*=*$1*=*");
        const split = _tagName.split("*=*");
        const handler = split.map(word => {
          if (word === keyword) return { text: word, type: 'HIGH' }
          return { text: word, type: 'DEFAULT' }
        });
        return [...result, { ...items, label: handler }];
      }, []);
    },
    // 收藏
    collect(e) {
      const item = e.target.dataset['item'] || e.currentTarget.dataset['item']
      const index = e.target.dataset['index'] || e.currentTarget.dataset['index']
      console.log("点击收藏", index);
      this.triggerEvent('cardFun', {
        type: 'collect',
        index,
        data: item
      })
    },
    // 联系方式
    relation(e) {
      const item = e.target.dataset['item'] || e.currentTarget.dataset['item']
      this.triggerEvent('cardFun', {
        type: 'relation',
        data: item
      })
    },
    // 官网 
    official(e) {
      const item = e.target.dataset['item'] || e.currentTarget.dataset['item']
      if (item.official_website) {
        wx.setClipboardData({
          data: item.official_website,
          success(res) {
            wx.showToast({
              title: '复制成功',
              icon: 'none'
            })
          }
        })
      } else {
        wx.showToast({
          title: '该企业暂无官网',
          icon: 'none'
        })
      }
    },
    // 发地址 
    site(e) {
      const item = e.target.dataset['item'] || e.currentTarget.dataset['item']
      console.log(item)
      this.triggerEvent('cardFun', {
        type: 'site',
        data: item
      })
    },
    loadMore() {
      this.triggerEvent('loadMore')
    },
    handleRefresher() {
      this.triggerEvent('refresh')
    },
    goDetail(e) {
      console.log(e, 222);
      let { ent_id, ent_logo, ent_name } = e.currentTarget.dataset.item;
      const { isLogin } = this.data;
      if (ent_id) {
        // const url = `/subPackage/pages/companyDetail/companyDetail?id=${ent_id}`;
        // app.route(this, url)
        const url = encodeURIComponent(`https://reporth5.handidit.com?entId=${ent_id}`)
        app.route(this, `/subPackage/pages/webs/index?url=${url}`)
        // 触发历史记录事件
        const params = {
          behavior_history_mode: "FINANCING_EVENT",
          enterprise_id: ent_id,
          enterprise_log: ent_logo,
          enterprise_name: ent_name,
          model_type: 'BUSINESS',
          second_model_type: 'ENTERPRISE'
        };
        if (!ent_logo) params.enterprise_log = "11";
        isLogin && home.addBevHis(params) //新增浏览历史 
        this.triggerEvent('handleTit');
      }
    }
  }
})