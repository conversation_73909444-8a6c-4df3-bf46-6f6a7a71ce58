import {
  home,
  common
} from '../../../service/api';
import {
  getHeight
} from '../../../utils/height';
import {
  handleShareUrl,
  getShareUrl
} from '../../../utils/mixin/pageShare'
var checkMixin = require('../../../utils/mixin/check')
const years = [];
const date = new Date();

for (let i = 1990; i <= date.getFullYear(); i++) {
  years.unshift({
    code: i,
    name: i
  })
}
years.unshift({
  code: '',
  name: '不限'
})
const app = getApp();
Page({
  behaviors: [checkMixin()],
  data: {
    scrollHeight: '700',
    keyword: '',
    // 请求相关
    params: {
      keyword: '',
      theme_type: '',
      year: '',
      page_index: 0, //偏移量
      page_size: 10, //每页多少条
    },
    list: [], //获取列表
    isFlag: true, //节流 true允许
    hasData: true, //  是否还有数据
    isNull: false, // list长度是否为0
    isTriggered: false, // 下拉刷新状态
    isTap: false,
    hasFilter: false,
    selectData: [{
        code: 'year',
        name: '选择年份',
        defaultValue: '',
        type: 'radio',
        children: years
      },
      {
        code: 'theme_type',
        defaultValue: '',
        name: '榜单类型',
        type: 'radio',
        children: []
      },
    ],
  },
  onLoad: function (options) {
    handleShareUrl()
    const {
      keyword = '',
        template
    } = options;
    this.setData({
      'params.keyword': keyword,
      keyword
    });
    app.showLoading('加载中');
    this.initList();
    this.getTypeDict()
    if (template) {
      app.showLoading('加载中');
      this.go(JSON.parse(template))
    }
  },
  onReady() {
    this.scrollH()
  },
  scrollH() { // 动态获取页面高度 
    var that = this;
    getHeight(that, ['.content_height'], (data) => {
      const {
        res,
        screeHeight
      } = data
      const height = screeHeight - res[0].top
      this.setData({
        scrollHeight: height
      })
    })
  },
  onSelect(e) {
    let obj = e.detail
    let _this = this
    let key = (Object.keys(obj))[0],
      val = obj[key]
    if (key === 'year') {
      _this.setData({
        'selectData[0].defaultValue': val,
        'params.year': val
      }, () => {
        this.getData()
      })
    } else {
      _this.setData({
        'selectData[1].defaultValue': val,
        'params.theme_type': val
      }, () => {
        this.getData()
      })
    }

  },
  handleInput(e) {
    let keyword = e.detail
    if (keyword || keyword == '') {
      this.setData({
        'params.keyword': keyword,
      });
    }
    this.getData()
  },
  async getDetailData(data) {
    let {
      data_source,
      id,
      type,
      theme_type
    } = data;
    try {
      app.showLoading('加载中...')
      let res1 = await home.getDefinitiveTop({
        data_source,
        id,
        theme_type,
        type,
        page_index: 1, //偏移量
        page_size: 500, //每页多少条
      })
      let arr = res1?.items?.map(item => {
        let {
          tags,
          fa_ren,
          reg_cap,
          es_date,
          ent_name,
          ent_logo,
          collected,
          ent_id,
          location,
          region,
          official_website
        } = item
        tags = tags.filter((tag, ind) => ind < 3);
        return {
          tags,
          legal_person: fa_ren,
          register_capital: reg_cap,
          register_date: es_date,
          ent_name,
          logo: ent_logo,
          collect: collected,
          ent_id,
          location,
          region,
          official_website
        };
      }) || []
      return JSON.stringify(arr)
    } catch (error) {
      if (error.data.code.indexOf('BusinessPackageErrorCode/OUT_LIMIT_REQUEST_TO_VIP') > 0) {
        this.setData({
          visible: true
        })
      }
      wx.hideLoading()
      return
    }
  },
  async go(e) {
    // 用于详情页面分享
    let template = {
      currentTarget: {
        dataset: {
          item: e.currentTarget.dataset.item
        }
      }
    }
    // 权限判断
    let {
      keyword,
      login
    } = this.data;
    let {
      type,
      id,
      data_source,
      name,
      release_date,
      source
    } = e.currentTarget.dataset.item
    const tempObj = {
      type,
      id,
      data_source,
      name,
      release_date,
      source,
      theme_type: name
    };
    // 判断是否登录 
    if (!login) {
      app.route(this, '/pages/login/login')
      return
    }
    wx.removeStorageSync('author')
    // 判断是否有权限继续查看详情页面
    let res = await this.getDetailData(tempObj)
    if (!res) return;
    // 本地存储 
    wx.setStorageSync('author', encodeURIComponent(res))
    if (keyword) {
      home.addBevHis({
        enterprise_name: name,
        enterprise_id: id,
        behavior_history_mode: 'INDEX_PAGE',
        enterprise_log: "-",
        model_type: 'BUSINESS',
        second_model_type: 'LISTED',
        extra_param: JSON.stringify(tempObj)
      }) //新增浏览历史 
    }
    let obj = encodeURIComponent(JSON.stringify(tempObj));
    wx.hideLoading()
    // app.route(this, `/companyPackage/pages/authoritativeDetail/authoritativeDetail?params=${obj}&res=${encodeURIComponent(res)}&template=${JSON.stringify(template)}`, 'navigateTo')
    app.route(this, `/companyPackage/pages/authoritativeDetail/authoritativeDetail?params=${obj}&template=${JSON.stringify(template)}`, 'navigateTo')
  },
  unLocked() {
    wx.hideKeyboard();
    this.setData({
      loading: false
    })
  },
  getData() {
    app.showLoading('加载中');
    this.refresh();
  },
  // 下拉刷新
  refresh() {
    const that = this;
    wx.showNavigationBarLoading()
    let {
      params
    } = that.data
    let obj = {
      ...params,
      page_index: 0,
      page_size: 10
    }
    that.setData({
      // isTriggered: true,
      hasData: true, // 推荐企业是否还有数据
      list: [], // 推荐企业列表
      params: obj, // 推荐企业查询参数
    }, () => that.initList(() => {
      that.setData({
        isTriggered: false
      })
      wx.hideNavigationBarLoading()
      that.scrollH()
    }));
  },
  // 加载更多
  loadMore() {
    const that = this;
    let {
      params,
      hasData,
      isFlag
    } = that.data;
    if (!hasData) return;
    if (!isFlag) return; //节流
    params.page_index += 1;
    that.setData({
      params
    }, () => that.initList(() => {
      that.scrollH()
    }));
  },
  arrfiy(key, name) {
    if (!key) return [name];
    return name.replace(new RegExp(`${key}`, 'g'), `%%${key}%%`).split('%%');
  },
  // 获取列表数据
  initList(callback) {
    const that = this;
    let {
      list,
      hasData,
      params,
    } = that.data;
    that.setData({
      isNull: false,
      isFlag: false
    });
    home.getDefinitiveList(params).then(res => {
      let {
        items,
        count
      } = res
      if (items.length < params.page_size) hasData = false;
      items = items.map(item => {
        item.names = this.arrfiy(params.keyword, item.name);
        return item;
      })
      that.setData({
        list: list.concat(items),
        hasData,
        isFlag: true,
        count
      }, () => {
        if (!that.data.list.length) {
          that.setData({
            isNull: true,
            hasData: true,
            count: 0
          });
        }
      })
      callback && callback()
    }).catch(err => {
      callback && callback()
      app.showToast('获取数据失败!请稍后再试')
      that.setData({
        list: [],
        isNull: true,
        hasData: true,
        count: 0
      });
    }).finally(() => {
      wx.hideLoading();
    })
  },
  // 获取榜单类型数据
  getTypeDict() {
    common.definitiveThemeTypeDict().then(res => {
      let {
        dictionary_data
      } = res
      let data = dictionary_data?.map(item => {
        let {
          code,
          message
        } = item
        return {
          code,
          name: message
        }
      })
      data.unshift({
        code: '',
        name: '全部'
      })
      this.setData({
        'selectData[1].children': data
      })
    }).catch(err => {
      app.showToast('获取榜单类型数据失败!请稍后再试')
    })
  },
  onShareAppMessage: function () {
    return {
      title: '邀请你查看2000+权威产业榜单', //自定义转发标题
      path: getShareUrl('/companyPackage/pages/authoritativeList/authoritativeList'), //分享页面路径
      imageUrl: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/aa1.png' //图片后面换
    };
  },
});