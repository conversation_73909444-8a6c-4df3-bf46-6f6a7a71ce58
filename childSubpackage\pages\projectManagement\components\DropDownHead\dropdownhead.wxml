<!-- 下拉组 -->
<view class="dropdown-head {{hasCheckbox && 'between'}}">
  <view wx:if="{{hasCheckbox}}" bindtap="handleCheckAll" class="check-all {{checked && 'checked'}}"></view>
  <view 
    class="item {{ item.showPop || item.hasValue ? 'active' : '' }}" 
    bindtap="handleClick"
    data-item="{{item}}"
    data-index="{{index}}"
    wx:for="{{headGroup}}" 
    wx:key="code">
    <text>{{item.name}}</text>
    
    <!-- 箭头 -->
    <image 
      class="icon" 
      src="{{item.showPop ? icons[1] : (!item.showPop && !item.hasValue) ? icons[0] : icons[2]}}"></image>
  </view>
</view>