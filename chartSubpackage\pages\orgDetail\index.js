import { getHeight } from '../../../utils/height';
import { getOrgInformation, getInvestmentEvent, getRelatedInfo,orgInvStatisticByRound, getOrgAnnualInvNum, orgInvStatisticBySubcatname, orgInvStatisticByRegion, orgRelationEnt } from "../../../service/financing";
import { formatterData, defaultColors, swiperData } from "./data";
import { moneyFormat } from "../../../utils/util";
const app = getApp()
Page({
  data: {
    defaultColors, 
    swiperData,
    navHeight: 0,
    headerHeight: 0,
    istrue_scroll: false, // 判断是否滚动
    isPreference: false,
    obj: {}, //当前详情页面的对象
    scrollViewHeight: 0, //外层内容滚动高度
    subScrollViewHeight: 0, // 内层内容滚动高度 
    contentT: 20, // 
    openSet: false, // 打开弹窗
    currentSwiper: 'section1',
    information: [], // 相关资讯列表
    informationCount: 0, // 相关资讯数量
    eventList: [], // 投资事件列表
    eventCount: 0,  // 投资事件数量
    yearPopupVisible: false,
    mapActive: 'province', // province; city
    dataLine: {},
    turnList: [], // 投资轮次
    treeMapData: [],
    areaList: [],
    allAareaList: [],
    region: {
      name: "全国",
      code: "1000000",
    },
    entList: [],
    oldTimeData: {},
  },
  onLoad(options) {
    app.showLoading('加载中...');
    const { org_id } = options;
    this.setData({ navHeight: app.globalData.navBarHeight, org_id }, () => {
      this.init();
    })
  },
  getRestHeight() {
    const { istrue_scroll, headerHeight } = this.data;
    return getHeight(this, ['#navigationBar', '#header', '#swipe_tab'], (data) => {
      let { screeHeight, res, } = data;
      this.setData({
        scrollViewHeight: screeHeight - res[0].height, //内容滚动高度
        // contentT: res[1].height,
        headerHeight: !istrue_scroll ? headerHeight : res[0].height  + res[1].height,
        subScrollViewHeight: screeHeight - headerHeight - res[2].height
      });
    });
  },
 
  // 判断上下滚动方向）（deltaY小于0时，向下，向上则反之）
  onScroll({ detail }) {
    const { currentSwiper } = this.data;
    const this_ = this;
    const { scrollTop  } = detail;
    this.setData({
      istrue_scroll: scrollTop > 0,
      contentT: scrollTop > 0 ? 0 : 20,
    }, () => {
      this_.getRestHeight();
    });
     
  },
  // tab切换
  handleTabChange(e) {
    const { dataset } = e.currentTarget;
    this.setData({
      currentSwiper: dataset.item.sectionId,
      intoViewX: dataset.item.sectionId,
    });
  },
  // 数据初始化
  async init() {
    // 初始化的无需额外参数的请求
    const [{ ent_id }] = await Promise.all([
      this.getOrgInfoData(),
      this.getOrgAnnualInvNum(),
      this.getInvestmentEvent(),
      this.getOrgInvStatisticByRound(),
      this.getOrgInvStatisticBySubcatname(),
      this.getOrgInvStatisticByRegion(),
      this.getOrgRelationEnt(),
    ]);
    // 然后请求需请求数据的请求
    await Promise.all([
      this.getRelatedInfo(ent_id),
    ]);
    // 阿巴阿巴阿巴 pieLegendConfig
    wx.hideLoading();
    this.getRestHeight();
  },
  // 获取机构详情
  getOrgInfoData() {
    const { org_id } = this.data;
    return getOrgInformation(org_id).then(res => {
      console.log('obj', res.org_logo);
      this.setData({ obj: res ?? {} });
      return res;
    })
  },
  // 投资机构关联企业查询
  getOrgRelationEnt() {
    const { org_id } = this.data;
    return orgRelationEnt({org_id, page_index: 1, page_size: 10}).then((res) => {
      this.setData({ entList: res.count && res.datalist || [] });
      return res;
    })
  },
  // 获取投资事件 
  getInvestmentEvent() {
    const { org_id } = this.data;
    const params = { org_id, page_index: "1", page_size: "3" };
    return getInvestmentEvent(params).then(({ count = 0, datalist = [] }) => {
      // 这里投资金额需要处理 (Copy 招商三的，具体啥规则不知道)
      datalist.map(record => {
        if (record.invest_detail_money != '0' && record.invest_currency_name) {
          record.moneyStr = moneyFormat(record.invest_detail_money, record.invest_currency_name);
        } else {
          record.moneyStr = record.invest_similar_money_name || '未透露';
        };
      })
      this.setData({ eventList: datalist, eventCount: count });
    });
  },
  // 投资数量
  getOrgAnnualInvNum(){
    const { org_id } = this.data;
    return getOrgAnnualInvNum({org_id}).then(res => {
      let data = [], legend = [];
      res.datalist.length && res.datalist.sort((a,b) =>  Number(a.year) - Number(b.year)).forEach(e => {
        data.push({ value: e.total});
        legend.push(e.year);
      });
      this.setData({
        dataLine: { value: [{data, name: "投资数量"}],legend}
      });
      return res;
    })
  },
  // 获取轮次分布  
  getOrgInvStatisticByRound(){
    const { org_id } = this.data;
    return orgInvStatisticByRound({org_id}).then(res => {
      const arr = formatterData(res.datalist ?? [], 'round_name', 'total')
      this.setData({ turnList: arr ?? [] });
      return res;
    })
  },
  // 获取偏好
  getOrgInvStatisticBySubcatname (){
    const { org_id,oldTimeData } = this.data;
    return orgInvStatisticBySubcatname({org_id, invest_date: oldTimeData?.value || '' }).then(res => {
      const arr = formatterData(res.datalist ?? [], 'sub_cat_name', 'total')
      this.setData({ treeMapData: arr ?? [] });
      return res;
    })
  },
  // 地区分布
  getOrgInvStatisticByRegion (){
    const { org_id, mapActive, allAareaList } = this.data;
    return orgInvStatisticByRegion({org_id, province: mapActive === 'province' ? 'true' : 'false'}).then(res => {
      const arr = formatterData(res.datalist ?? [], 'region', 'total')
      this.setData({ areaList: arr ?? [], allAareaList: mapActive === 'province' ? arr: allAareaList });
      return res;
    })
  },
  // 获取相关资讯
  getRelatedInfo(ent_id) {
    const { org_id } = this.data;
    const params = { org_id, ent_id, page_index: 1,  page_size: 10, };
    return getRelatedInfo(params).then(({ count = 0, items = [] }) => {
      this.setData({ information: items, informationCount: count });
    });
  },
  //  跳转企业详情
  goToEntDetail(e){
    let { item } = e.currentTarget.dataset;
    if(item.ent_id) {
      const url = encodeURIComponent(`https://reporth5.handidit.com?entId=${item.ent_id}`)
      app.route(this, `/subPackage/pages/webs/index?url=${url}`) 
    }
  },

  //  复制 TODO 联系方式
  handleCopy({ currentTarget: {dataset}}) {
    const { item } = dataset;
    if(item.url) {
      wx.setClipboardData({
        data: item.url,
        success(res) {
          wx.showToast({
            title: '复制成功!',
            icon: 'none'
          });
        },
      });
    }
  },
 
  // 查看全部标签
  openDialog({ currentTarget: {dataset}}) {
    const { type, title } = dataset
    this.setData({ openSet:true, dialogType: type,dialogTitle: title });
  },
  //  关闭
  tagClose() {
    this.setData({ openSet: false });
  },
  // 查看更多投资事件
  getMore({ currentTarget: {dataset}}) {
    const { type } = dataset
    const { org_id, obj } = this.data;
    let url = '';
    if(type === 'event') {
      url = `/subPackage/pages/organization/orgEvents/index?org_id=${org_id}`;
    } else {
      url = `/subPackage/pages/organization/information/index?org_id=${org_id}&ent_id=${obj.ent_id}`;
    }
    url && app.route(this, url)
  },
  errorFunction() {
    const { obj } = this.data;
    this.setData({ obj: { ...obj } })
  },
  showPreferenceInfo({currentTarget: {dataset}}){
    const { type } = dataset
    const { isPreference } = this.data;
    if(type === 'close') {
      this.setData({ isPreference: false })
    } else {
      this.setData({ isPreference: !isPreference })
    }
  },
  // 地区分布省份切换
  changeMapActive({ currentTarget: {dataset}}){
    const this_ = this;
    const { type } = dataset;
    this.setData({ mapActive: type }, () => this_.getOrgInvStatisticByRegion());
  },
  //  投资偏好 - 年份选择
  changeYear(){
    this.setData({ yearPopupVisible: true })
  },
  // 查看全部点击关闭
  onHandlertrigger({detail}){
    const this_ = this;
    this.setData({ 
      yearPopupVisible: false, 
      oldTimeData: detail  
    }, () => this_.getOrgInvStatisticBySubcatname())
  }
})