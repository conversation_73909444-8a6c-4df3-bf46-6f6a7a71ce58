.wrap {
  height: 100vh;
  overflow: hidden;
  background: #f4f4f4;
}

/* input */
.searbox {
  width: 100%;
  background: #fff;
  padding: 20rpx 24rpx;
  /* border: 1px solid red; */
  margin-bottom: 20rpx;
}

.searchs {
  position: relative;
  z-index: 31;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #eeeeee;
  border-radius: 8rpx;
  padding: 16rpx 24rpx;
  /* border: 1px solid red; */
}

.s-input {
  display: flex;
  align-items: center;
  flex: 1;
}

.s-input-img {
  width: 36rpx;
  height: 36rpx;
}

input {
  caret-color: #E72410;
  color: #9B9EAC;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
}

.s-input-item {
  display: flex;
  align-items: center;
  position: relative;
  flex: 1;
  height: 40rpx;
  padding-left: 16rpx;
}

.s-input-itemss::after {
  content: "";
  height: 64rpx;
  width: 2px;
  background: #DEDEDE;
  position: absolute;
  right: 0;
  transform: scale(0.5);
}

.s-input-item-i {
  position: relative;
  flex: 1;
  height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #05070c;
}

.placeholder {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #9b9eac;
  line-height: 40rpx;
}

.search-cancel {
  height: 40rpx;
  line-height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: RIGHT;
  color: #20263a;
  padding: 0 28rpx;
}

.input-clear {
  display: flex;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  /* border: 1px solid red; */
}

.search-cancel {
  height: 40rpx;
  line-height: 40rpx;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: RIGHT;
  color: #20263a;
  padding: 0 28rpx;
}

/* 列表 */
.operation-wrapper {
  width: 100%;
  overflow-y: auto;
  padding-bottom: 40rpx;
}

.operation-wrapper .operation-item {
  max-height: auto;
  background: #fff;
  padding: 0 24rpx;
  margin-bottom: 20rpx;
  transition: max-height 0.4s linear;
  overflow-y: hidden;
}

.operation-wrapper .operation-item.show {
  max-height: 96rpx;
}

.operation-wrapper .null-wrapper .null {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}

.operation-wrapper .null-wrapper image {
  width: 300rpx;
  height: 300rpx;
}

.operation-wrapper .null-wrapper .text {
  margin-top: 20rpx;
}

.operation-wrapper .operation-item .operation-item-title {
  line-height: 42rpx;
  font-size: 30rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
}

.operation-wrapper .operation-item-child {
  line-height: 42rpx;
  font-size: 30rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  color: #74798C;
}

.operation-wrapper .operation-item .item-row {
  padding: 28rpx 0 26rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.operation-wrapper .operation-item .item-row image {
  width: 20rpx;
  height: 20rpx;
}

.operation-wrapper .operation-item .item-row text {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  word-break: break-all;
}

.operation-wrapper .operation-item .item-row:not(:last-child) {
  border-bottom: 2rpx solid #EEEEEE;
}