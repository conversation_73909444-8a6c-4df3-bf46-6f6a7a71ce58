import {
  searInputConstant,
  searMultIptConstant,
  renderList,
  checkoutSear,
  handlestructure,
  searCheckPopConstant,
  params,
  handleMultiple
} from '../../../../../components/hunt/mixin'
import {
  clone
} from '../../../../../utils/util'
var behavior = require('../../../../../template/menuhead/index')
var huntBehavior = require('../../../../../components/hunt/common/common')
const app = getApp()

let searchDataList = renderList.filter(i => {
  if (i.title !== '基础筛选') return i;
}).filter(i => ((i.type != 'areas' && i.type != 'trade_types' && i.type != "ent_name"))).filter(i => {
  // if (i.title == "基础筛选") {
  //   i.map = i.map.filter(itm => (itm.key != 'areas' && itm.key != 'trade_types' && itm.key != "ent_name"))
  //   return i
  // }
  return i
})
// console.log('searchDataList', searchDataList);
let filterParams = (function (params) {
  delete params['ent_name'];
  delete params['regionData'];
  delete params['eleseic_data'];
  return params
})(params)

Component({
  behaviors: [huntBehavior, behavior],
  properties: {
    dropDownMenuTitle: {
      type: Array,
      value: [],
    },
    height: { //用于动态计算遮罩层高度
      type: Number || String,
      value: 0,
      observer: function (newVal, oldVal) {
        if (newVal != oldVal) {
          this.handleHeight()
        }
      }
    },
    isLocation: { // 是否已经授权
      type: Boolean,
      value: false,
    },
    dropDownMenuDistrictData: { //地区
      type: Array,
      value: [],
      // observer: function(newVal, oldVal) {处理数据
      //   let model = newVal[0] ? newVal[0] : null
      //   this.selectDefaltDistrictLeft(model)
      // }
    },

    dropDownMenuSourceData: { //全部行业 
      type: Array,
      value: []
    },
    dropDownMenuFilterData: { //更多筛选
      type: Array,
      value: []
    },
    hideClass: {
      type: Boolean,
      value: false,
    },
  },
  data: {
    sliderVal: 0,
    // 行业相关
    checkedList: [],
    oldData: "",
    district_val: false,
    source_val: false,
    filter_val: false,
    // 筛选相关
    itemList: JSON.parse(JSON.stringify(searchDataList)), // 页面静态数据
    leftList: JSON.parse(JSON.stringify(searchDataList)), // 页面静态数据
    params: filterParams,

    //对上面那个在做一次缓存
    tempParams: {},
  },
  observers: {
    checkedList: function (list) {
      console.log(list.length > 0)
      if (list.length > 0) {
        this.setData({
          district_val: true
        })
      } else {
        this.setData({
          district_val: false
        })
      }
    },
    sliderVal: function (val) {
      if (val >= 1) {
        this.setData({
          source_val: true
        })
      } else {
        this.setData({
          source_val: false
        })
      }
    },
    filter_open: function (filter_open) { //点击上面保留状态
      const {
        tempParams
      } = this.data
      this.setBackfillData(tempParams)
    },
    tempParams: function (obj) { //更多筛选
      this.result(obj)
    },
    vipVisible: function (val) {
      // 通过事件传递的方式告诉外面，需要vip弹窗
      console.log('是否需要弹vip弹窗', val)
      this.triggerEvent('vip', true)
    }
  },
  methods: {
    tapDistrictNav: function (e) {
      if (!this.data.isLocation) return;
      if (this.data.source_open || this.data.filter_open) return
      if (this.data.district_open) {
        this.setData({
          district_open: false,
          source_open: false,
          filter_open: false,
          shownavindex: 0
        })
      } else {
        this.setData({
          district_open: true,
          source_open: false,
          filter_open: false,
          shownavindex: e.currentTarget.dataset.nav
        })
      }
    },
    tapSourceNav: function (e) {
      if (!this.data.isLocation) return;
      if (this.data.district_open || this.data.filter_open) return

      if (this.data.source_open) {
        this.setData({
          source_open: false,
          district_open: false,
          filter_open: false,
          shownavindex: 0
        })
      } else {
        this.setData({
          source_open: true,
          district_open: false,
          filter_open: false,
          shownavindex: e.currentTarget.dataset.nav
        })
      }
    },
    tapFilterNav: function (e) {
      if (!this.data.isLocation) return;
      if (this.data.source_open || this.data.district_open) return

      if (this.data.filter_open) {
        this.setData({
          source_open: false,
          district_open: false,
          filter_open: false,
          shownavindex: 0
        })
      } else {
        this.setData({
          source_open: false,
          district_open: false,
          filter_open: true,
          shownavindex: e.currentTarget.dataset.nav
        })
      }
    },

    getArea(e) {
      let arr = JSON.parse(JSON.stringify(e.detail)),
        name, codeList;
      name = arr.sort((a, b) => (a.level - b.level)).map(i => i.name).join(',').slice(0, 4) + '...'
      codeList = handleMultiple(arr).filter(item => item.status === 'checked').map(item => item.code)
      if (name == '...') {
        name = '全部行业'
      }
      this.setData({
        district_open: false,
        oldData: arr,
        checkedList: arr,
        "dropDownMenuTitle[0]": name,
      })
      this.triggerEvent("industry", codeList)
    },
    closeRegion(e) {
      this.closeHyFilter()
    },
    onSliderChange(e) {
      const val = e.detail.value
      // 这里应该还要发请求 获取数据 渲染好多家相关企业
      this.setData({
        sliderVal: val,
      })
      this.triggerEvent("slider", {
        slider: val + 1
      })
    },
    // 重置筛选条件
    resetCondition(isHeight) {
      //isHeight表示从高级搜索过来的 --需要重新搞 
      let obj = clone(params)
      delete obj['regionData']
      delete obj['eleseic_data']
      let data = {
        itemList: JSON.parse(JSON.stringify(searchDataList)), //重置状态
        params: obj, //这里的params值为空数组那部分怎麽拷貝都有問題
        minCapital: '',
        maxCapital: '',
        capitalActive: false,
        minDate: '',
        maxDate: '',
        dateActive: false,
        socialActive: false,
        socialminPeson: "",
        socialmaxPeson: "",
      }
      if (isHeight) { //将地区和行业重置为空数组
        data.regionData = [],
          data.industryData = []
        data.ent_name = ''
      }
      this.setData(data, () => {
        !isHeight && this.result(this.data.tempParams)
      })
    },
    clearSear() { //这里不能重置掉全国和全部行业
      this.resetCondition(false)
    },
    result(val) { //自定义需要单独调用，弹窗那种也需要调用
      let isHeight = false //是否有选中的 外面设置高亮要用
      let paramsData = clone(val) ? clone(val) : clone(this.data.params);
      // 排除地区，交通，企业名字因为这里设置高亮
      delete paramsData['areas']
      delete paramsData['trade_types']
      delete paramsData['ent_name']
      Object.keys(paramsData).some(keys => { //这里如果遇到以前的数据可能会报错，所以以前的数据后端要清除没法兼容
        // 这种有自定义需要单独处理
        if (searCheckPopConstant['list'].includes(keys) || searMultIptConstant['list'].includes(keys)) {
          if (paramsData[keys].length > 0 && (paramsData[keys][0]?.start || paramsData[keys][0]?.end)) {
            isHeight = true
            return true
          } else if (searInputConstant['list'].includes(keys)) {
            if (paramsData[keys].trim().length > 0) {
              isHeight = true
              return true
            }
          } else {
            isHeight = false
          }
        } else if (paramsData[keys].length > 0) {
          isHeight = true
          return true
        }
      })
      //把结果抛出去  
      // console.log(isHeight)
      this.setData({
        filter_val: isHeight
      })
      // this.triggerEvent('submit', {
      //   isHeight,
      //   paramsData: clone(paramsData)
      // })
      return isHeight
    },
    // 更多筛选确定
    search() {
      let {
        itemList,
        minCapital,
        maxCapital,
        minDate,
        maxDate,
        socialminPeson,
        socialmaxPeson,
        curfilterData,
        filter_val
      } = this.data; // 当前页参数
      let params = clone(this.data.params);
      if (filter_val && !checkoutSear(params)) {
        return
      }
      for (let item of itemList) {

        // 注册资本
        item.type === 'register_capital' && this.setParmas(item, params, minCapital, maxCapital);
        // 参保人数
        item.type === 'super_dimension_social_num' && this.setParmas(item, params, socialminPeson, socialmaxPeson);

        // 注册时间
        item.type === 'register_time' && this.setParmas(item, params, minDate, maxDate);
      }
      // 开始和结束都没选齐的情况
      searMultIptConstant['list'].forEach(i => {
        if (params[i][0] && params[i][0].start == '' && params[i][0].end == '') {
          params[i] = []
        }
      })
      searCheckPopConstant['list'].forEach(i => {
        if (params[i][0] && params[i][0].start == '' && params[i][0].end == '') {
          params[i] = []
        }
      })
      params['ent_type'] = params.enttype_data?.length > 0 ? handleMultiple(params.enttype_data).filter(i => i.status === 'checked').map(i => i.code) : []
      params['ent_cert'] = params.all_cert_data?.length > 0 ? handleMultiple(params.all_cert_data).filter(i => i.status === 'checked').map(i => i.code) : []
      params['chain_codes'] = params.chain_codes_data?.length > 0 ? params.chain_codes_data.filter(i => i.active === true).map(i => i.code) : []

      curfilterData = Object.assign({}, params);

      let tempParams = JSON.parse(JSON.stringify(curfilterData)) //这里的tempParams和高级搜索哪里不一样 

      this.setData({
        curfilterData,
        filter_val,
        params,
        tempParams
      }, () => this.backAll())
    },
    // 总的返回
    backAll() {
      const {
        curfilterData
      } = this.data
      let endFilterData = handlestructure(curfilterData)
      delete endFilterData['enttype_data']
      delete endFilterData['chain_codes_data']
      const requestData = {
        ...endFilterData //更多筛选
      }
      this.triggerEvent('search', requestData)
      this.closeHyFilter();
    }
  },
  //组件生命周期函数，在组件实例进入页面节点树时执行
  attached: function () {
    this.handleHeight()
  },
  pageLifetimes:{
    hide(){
      this.setData({
        source_open: false,
        district_open: false,
        filter_open: false,
      })
    },
  }
})