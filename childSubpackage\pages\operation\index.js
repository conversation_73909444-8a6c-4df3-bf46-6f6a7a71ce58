const app = getApp()
import { publish } from "../../../service/api";
const { getAllManual } = publish;
Page({

  data: {
    list: []
  },
  onLoad() {
    app.showLoading();
    this.initData();
  },
  async initData() {
    await this.getAllManualClassList();
    wx.hideLoading();
  },
  // 获取操作说明列表
  getAllManualClassList(cb) {
    return getAllManual().then(({ class_list = [] }) => {
      this.setData({ list: class_list });
    });
  },
  goDetail(event = {}) {
    const { item = {} } = event.currentTarget.dataset;
    const { id } = item;
    app.route(this, `/childSubpackage/pages/operationDetail/index?id=${id}`);
  }
})