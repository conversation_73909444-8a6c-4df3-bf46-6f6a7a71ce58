module.exports = {
  moreListType: {
    "1": {
      src: "https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/chartSubpackage/image/phone.png"
    },
    "2": {
      src: "https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/chartSubpackage/image/tel.png"
    },
    "3": {
      src: "https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/chartSubpackage/image/mail.png"
    },
  },
  swiperData: [{
      value: '投资数量',
      sectionId: 'section1'
    },
    {
      value: '投资偏好',
      sectionId: 'section2'
    },
    {
      value: '轮次分布',
      sectionId: 'section3'
    },
    {
      value: '地区分布',
      sectionId: 'section4'
    },
    {
      value: '投资事件',
      sectionId: 'section5'
    },
    {
      value: '相关资讯',
      sectionId: 'section6'
    },
  ],
  defaultColors: ['#BFE3FB', '#BAEAE0', '#D7E4EC', '#C7D2F8', '#B4D7EE', '#DDE1EF', '#BAEAE0', '#F8EBCC', '#D5D0DA', '#D2E3D9', '#DDE1EF', '#F0D7C5', '#DDE1EF', '#69C3FE', '#7FBDE5', '#B8C4F1', '#57DDC1', '#96DBCC', '#FBE5AE', '#BAEAE0', '#EDC2A4', '#DDE1EF', '#F4CE6B'],
  formatterData(data, name, value) {
    return data.map((item) => {
      return {
        name: item[name],
        value: item[value]
      };
    });
  },
};