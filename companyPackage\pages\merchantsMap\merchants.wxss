.page {
  height: 100vh;
  overflow: hidden;
  font-family: PingFang SC, PingFang SC-Semibold;
}
.page_tit {
  position: relative;
  z-index: 100;
  width: 100%;
  height: 96rpx;
  background: #fff;
  border-bottom: 1rpx solid #eeeeee;
  border-top: 1rpx solid #eeeeee;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24rpx;
}
.page_tit .left {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}
.page_tit view {
  display: flex;
  align-items: center;
  height: 100%;
  justify-content: space-between;
}
.page_tit text {
  font-weight: 400;
  font-size: 28rpx;
  color: #20263a;
}

.page_tit .img1 {
  width: 32rpx;
  height: 32rpx;
  margin-right: 16rpx;
}

.page_tit .img2 {
  width: 20rpx;
  height: 20rpx;
}

wx-slider .wx-slider-handle-wrapper {
  height: 8rpx;
}

.br_line {
  width: 100%;
  height: 2rpx;
  background: #eee;
  transform: scaleY(0.5);
  position: relative;
  z-index: 11;
}

.merch {
  width: 702rpx;
  height: 72rpx;
  background: #eeeeee;
  border-radius: 8rpx;
  margin: 28rpx 24rpx;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  position: relative;
  z-index: 11;
}

.merch image {
  width: 40rpx;
  height: 40rpx;
  margin-left: 28rpx;
  margin-right: 20rpx;
}

.merch-input {
  width: 80%;
  font-size: 28rpx;
  color: #9b9eac;
}

.placeholder_class {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #9b9eac;
}

/* 滚动区域 */
.move_box {
  width: 100%;
  height: calc(100% - 720rpx);
}

.move_view {
  width: 100%;
  height: 110rpx;
  /* background: skyblue; */
}
.move_view .btn-wrap {
  height: 700rpx;
  background-color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
}
.move_view .btns {
  padding: 0;
  width: 350rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: linear-gradient(90deg, #ffb2aa 0%, #e72410 100%);
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  font-size: 32rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #ffffff;
  text-align: center;
}
.merchbox {
  width: 100%;
  padding: 16rpx 0 0;
  background: #ffffff;
  box-shadow: 0rpx 4rpx 18rpx 0rpx rgba(221, 221, 221, 0.5);
  border-radius: 14rpx;
}

.no_radius {
  border-radius: 0 !important;
  box-shadow: none !important;
}

.no_show {
  display: none !important;
}

.no_margin {
  margin: 0 !important;
}

.touch_bar {
  width: 80rpx;
  height: 6rpx;
  background: #eeeeee;
  border-radius: 4rpx;
  text-align: center;
  margin: 0 auto;
}

.touch_txt {
  padding-left: 24rpx;
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #20263a;
  margin-top: 28rpx;
}

/* 联系方式弹窗 */
.contact_box {
  width: 100%;
  height: 96rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

.contact_box:not(:last-of-type)::after {
  content: " ";
  width: 100%;
  height: 2rpx;
  background: #eeeeee;
  position: absolute;
  bottom: 0;
  transform: scaleY(0.5);
}

.contact_left {
  display: flex;
  align-items: end;
}

.contact_left image {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
}

.contact_number {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: LEFT;
  color: #e72410;
  /* line-height: 28rpx; */
}

.contact_right {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: CENTER;
  color: #74798c;
}

/* dialog文字样式 */
.dialog-con {
  font-size: 28rpx;
  font-family: PingFang SC, PingFang SC-Regular;
  font-weight: 400;
  text-align: CENTER;
  color: #74798c;
}

.dialog-con .map {
  position: relative;
  width: 100%;
  height: 112rpx;
  line-height: 112rpx;
  text-align: center;
  font-size: 34rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #e72410;
}

.weui-dialog__title {
  font-weight: 500 !important;
  color: #000000 !important;
  line-height: 40rpx;
  font-size: 34rpx !important;
}

.light_bd {
  padding: 0 !important;
}

.dialog-con .map::after {
  content: " ";
  width: 200%;
  height: 1rpx;
  background: #e5e5e5;
  position: absolute;
  top: 0;
  left: 0%;
  transform: scaleY(0.5);
}

.dialog-con .cancel {
  position: relative;
  height: 112rpx;
  line-height: 112rpx;
  text-align: center;
  font-size: 34rpx;
  font-family: PingFang SC-Medium, PingFang SC;
  font-weight: 500;
  color: #000000;
}

.dialog-con .cancel::after {
  content: " ";
  width: 200%;
  height: 1rpx;
  background: #e5e5e5;
  position: absolute;
  top: 0;
  left: 0%;
  transform: scaleY(0.5);
}

/* iOS设备特定的CSS---放大缩小 */
@supports (-webkit-touch-callout: none) {
  /*针对IOS的css*/
  movable-view {
    pointer-events: auto;
  }

  movable-area {
    pointer-events: none;
  }
}
