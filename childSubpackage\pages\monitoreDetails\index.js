var tabBeh = require('../../../template/tabBar/index')
var lFiterBeh = require('./common/lFilter/index')
var rFiterBeh = require('./common/lFilter/rmixin')
var lbxqMixn = require('./common/lFilter/reqMixin')
var yuqinMixn = require('./common/lFilter/reqMixin')
import {
  getHeight
} from '../../../utils/height'
import {
  daily
} from '../../../service/api'
const app = getApp()
Page({
  behaviors: [
    tabBeh,
    lFiterBeh,
    rFiterBeh,
    lbxqMixn({
      pname: 'lbxq',
      requestFunc: daily.fxdtList
    }),
    yuqinMixn({
      pname: 'yuqin',
      requestFunc: daily.yqdtList
    })
  ],
  data: {
    isFixed: false,
    isDrawing: false, //是否渲染
    login: app.globalData.login,
    contentH: 0, //内容滚动高度
    navigationBarH: app.globalData.navBarHeight, //导航顶部高度
    activeIndex: '0',
    entid: null,
    tabs: [{
        title: '风险动态',
        name: 'capital',
      },
      {
        title: '舆情动态',
        name: 'polishing',
      },
    ],
    groupId: null, //这个可能用不到
    objData: {}
  },
  onLoad(option) {
    app.showLoading('加载中...')
    let {
      entid,
      groupId
    } = option
    this.getRestHeight()
    this.getHeight()
    this.getContentPos()
    this.setData({
      entid: entid,
      'lbxqParms.groupId': groupId,
      'yuqinParms.groupId': groupId,
      'lbxqParms.entids': entid,
      'yuqinParms.entids': entid
    }, () => {
      this.conpanyInfo()
      this.getlbxqList()
      this.getyuqinList()
      setTimeout(() => wx.hideLoading(), 1300)
    })
  },
  onShow() {},
  async conpanyInfo() {
    try {
      const res = await daily.headInfo(this.data.entid)
      this.setData({
        objData: res
      })
    } catch (err) {
      console.log(err)
    }
  },
  onScroll(e) {
    const {
      navTopH
    } = this.data
    var scrollTop = e.detail.scrollTop
    if (navTopH && (scrollTop >= navTopH)) {
      if (this.data.isFixed) return
      this.setData({
        isFixed: true
      })
    } else {
      if (!this.data.isFixed) return
      this.setData({
        isFixed: false
      })
    }
  },
  goTop() {
    this.setData({
      scrollTop: this.data.navTopH + 2 //+ 2 //这像素解决的是首页滚动到顶部怕不能触发fixed 导致弹窗和固定定位有空隙就可以滑动的样式问题
    })
  },
  // 滚动
  getRestHeight() {
    getHeight(this, ['#navigationBar'], (data) => {
      let {
        screeHeight,
        res,
      } = data
      // console.log('navigationBarHB', res[0].height);
      this.setData({
        contentH: screeHeight - res[0].height, //内容滚动高度
        // navigationBarH: res[0].height,
        //导航顶部高度
      })
    })
  },
  getHeight() {
    getHeight(this, ['.h_head', '#navbars', '.navigationBar'], (data) => {
      let {
        screeHeight,
        res,
        statusBarHeight
      } = data
      // console.log('navBar', JSON.stringify(res[1]))
      let navigationBarH = res[0]?.top //自定义状态栏高度
      let navBarH = res[1]?.height //tab栏自身高度 
      let navTopH = res[1]?.top - navigationBarH // nab高度 
      // let resH = screeHeight - navBarH - res[1]?.top + 180
      let popResH = screeHeight - navBarH - res[2].height
      // console.log(navBarH, res[2].height)
      this.setData({
        // navigationBarH,
        navTopH,
        // resH,
        popTopH: navBarH + res[2].height - 4,
        popResH: popResH + 4
      })
    })
  },
  getContentPos() {
    getHeight(this, ['.content'], (data) => {
      this.setData({
        contentTop: data.res[0].top * 2
      })
    })
  },
  goTop() {
    this.setData({
      scrollTop: this.data.navTopH + 2 //+ 2 //这像素解决的是首页滚动到顶部怕不能触发fixed 导致弹窗和固定定位有空隙就可以滑动的样式问题
    })
  },
  tabClick(e) {
    let {
      title: detail
    } = e.currentTarget.dataset
    if (!detail) return
    let idx = detail == '风险动态' ? 0 : 1;
    this.getTabWidth(idx)
    // 把所有弹窗关闭 
    this.polishingcloseHyFilter()
    this.yuqincloseHyFilter()

    this.setData({
      activeIndex: idx,
      // scrollTop: this.data.navTopH
    }, () => {
      this.getHeight()
    })
  },
  loadMores() {
    this.loadMore(this.data.activeIndex == 0 ? 'lbxq' : 'yuqin')
  },
  onfresherss() {
    this.onfresher(this.data.activeIndex == 0 ? 'lbxq' : 'yuqin')
  },
  // 弹窗相关
  onFlitter(type, obj) { // 筛选
    app.showLoading('加载中...')
    if(obj['riskCls']){
      obj['riskType']=obj['riskCls']
      delete obj['riskCls']
    }
    this.setData({
      [`${type}IsFlag`]: true, //节流
      [`${type}HasData`]: true, //  是否还有数据
      [`${type}IsNull`]: false,
      [`${type}Parms`]: {
        pageIndex: 1, //偏移量 
        pageSize: 10, //每页多少条
        groupId: this.data[`${type}Parms`].groupId,
        entids: this.data[`${type}Parms`].entids,
        ...obj
      }
    }, () => {
      this['get' + type + 'List'](() => {}, true)
      setTimeout(()=>{
        wx.hideLoading()
      },1100)
    })
  },
  // 其它
  errorFunction() {
    this.setData({
      'objData.logo': "https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/default.png",
    })
  },
  official(e) {
    const item = e.target.dataset['item'] || e.currentTarget.dataset['item']
    if (item.official_website) {
      wx.setClipboardData({
        data: item.official_website,
        success(res) {
          wx.showToast({
            title: '复制成功',
            icon: 'none'
          })
        }
      })
    } else {
      wx.showToast({
        title: '该企业暂无官网',
        icon: 'none'
      })
    }
  },
  site(e) {
    const comDetail = e.target.dataset['item'] || e.currentTarget.dataset['item']
    this.setData({
      location: {
        lat: +comDetail.location.lat,
        lon: +comDetail.location.lon,
      },
      locationTxt: comDetail.register_address,
      addmarkers: [{
        id: 1,
        latitude: +comDetail.location.lat,
        longitude: +comDetail.location.lon,
        iconPath: "https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/marker.png",
        width: 20,
        height: 20,
      }],
      showAddress: true,
      locationMap: {
        latitude: +comDetail.location.lat, //维度
        longitude: +comDetail.location.lon, //经度
        name: comDetail.register_address, //目的地定位名称
        scale: 15, //缩放比例
        address: comDetail.register_address //导航详细地址
      }
    })
  },
  goMap() {
    const {
      locationMap
    } = this.data
    wx.openLocation(locationMap)
  },
  baogao(e) { //点击进入企业报告
    const comDetail = e.target.dataset['item'] || e.currentTarget.dataset['item']
    if (!comDetail.ent_id) {
      return
    }
    const url = encodeURIComponent(`https://reporth5.handidit.com?entId=${comDetail.ent_id}`)
    app.route(this, `/subPackage/pages/webs/index?url=${url}`)
  },
  onCloseAddress() {
    this.setData({
      showAddress: false
    })
  },
})

// 到时需要改得几个点，接口以及接口参数（重置接口参数那些）