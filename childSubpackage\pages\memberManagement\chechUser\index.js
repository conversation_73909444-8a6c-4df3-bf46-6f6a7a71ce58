
import { userAudit } from '../../../../service/user'
Page({

  /**
   * 页面的初始数据
   */
  data: {
    list: [],
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    this.getUserAudit();
  },
  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
  },
  async getUserAudit() {
    const { org_id } = wx.getStorageSync('userData')
    const res = await userAudit(org_id);
    this.setData({
      list: res.data || []
    })
  },
  // 拒绝加入
  handleRefused(){
    this.getUserAudit();
  },
  // 同意加入
  handleConsent(){
    this.getUserAudit();
  },
  // 删除
  handleDelete(){
    this.getUserAudit();
  }
})