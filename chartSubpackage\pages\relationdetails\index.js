import {
  getHeight
} from '../../../utils/height';
import {
  getShareUrl
} from '../../../utils/mixin/pageShare'

const app = getApp();
Page({
  data: {
    tabs: [{
        title: '关系图谱',
        name: 'atlas'
      },
      {
        title: '关系列表',
        name: 'sol'
      },
    ],
    tabIdx: 0,
    shareImg: '', //canvas生成的临时路径
    openSet: false, //重新获取权限,
    // 关系列表相关 
    connecObj: {}, //传进来的数据 
    allPathList: [],
    isNull: false,
    nulls: false,
  },
  onShareAppMessage: function () {
    let {
      shareImg,
      connecObj
    } = this.data
    let data;
    if (Object.keys(connecObj).length > 0) {
      data = encodeURIComponent(JSON.stringify(connecObj))
    }
    return {
      title: '一键查询 关联关系', //自定义转发标题
      path: getShareUrl(`/companyPackage/pages/mineRelation/relation`), //分享页面路径-分享图片打开了就是一张图片
      imageUrl: shareImg != '' ? shareImg : ''
    };
  },
  onShow() {
    const that = this;
    getHeight(that, '.zhanwei2', (res) => {
      this.setData({
        canvasHeight: res.MaskUsableHeight
      })
    })
  },
  onLoad(options) {
    let str = options?.params
    // wx.setStorageSync('ceshi', str)
    // let str = wx.getStorageSync('ceshi')
    if (str && str != '') {
      var obj = JSON.parse(decodeURIComponent(str))
      if (Object.keys(obj).includes('table') && obj['table'].length > 0) {
        this.setData({
          connecObj: obj,
          null: false
        }, () => {
          // 处理路径 
          this.handleLine()
        })
      } else {
        this.setData({
          nulls: true,
          isNull: true
        })
      }
      console.log('onLoad -----', this.data);

    } else {
      this.setData({
        nulls: true,
        isNull: true
      })
    }
  },
  // 方法
  getHeight(res) {
    const restHeight = wx.getSystemInfoSync().windowHeight - res.detail.height - 1
    this.setData({
      restHeight
    })
  },
  handleLine() { //处理数据结构
    const {
      connecObj,
      allPathList
    } = this.data
    let {
      table
    } = connecObj
    let tabList = [],
      relationChainData = [],
      curPathList = [],
      allPaths = [];
    if (!table || table.length <= 0) {
      this.setData({
        isNull: true
      })
      return
    }
    relationChainData = table.map(path => {
      !path.parent && tabList.push(path);
      return path;
    })
    // relationChainData 总的路径
    // console.log('tabList', tabList); //一共几条路径
    tabList.forEach(i => {
      curPathList = relationChainData.filter(item => {
        let {
          investment_money,
          investment_scale
        } = item;
        item.investment_money = investment_money && parseInt(investment_money)
        var num = investment_scale && investment_scale.split('%')[0]
        item.investment_scale = num && parseInt(num).toFixed(2) + '%';
        return item.parent === i.id;
      }); // 默认展示路径1
      allPaths.push(curPathList)
    });
    const result = allPaths.length > 100 ? this.getNewArray(allPaths, 100) : []
    console.log('result', result);
    // for(let i = 0; i< result.length; i++) {
    //   this.setData({
    //     allPathList: [...this.data.allPathList, ...result[i]]
    //   });
    //   console.log('this.data.allPathList', i, this.data.allPathList);
    // }
    this.setData({
      relationChainData, //原始列表
      allPathList: allPaths.splice(0, 300)
    })
  },
  getNewArray(arr, size) { // size要分割的长度
    const arrNum = Math.ceil(arr.length / size, 10); // Math.ceil()向上取整的方法，用来计算拆分后数组的长度
    let index = 0; // 定义初始索引
    let resIndex = 0; // 用来保存每次拆分的长度
    const result = [];
    while (index < arrNum) {
      result[index] = arr.slice(resIndex, size + resIndex);
      resIndex += size;
      index++;
    }
    return result;
  },
  clickCard(e) { //跳转然后重绘canvas
    const that = this;
    const {
      item
    } = e.currentTarget.dataset
    if (item.length > 0) {
      let road_no = item[0].road_no
      that.setData({
        'connecObj.idx': road_no,
        tabIdx: 0
      })
    }
  },
  tabchange(e) {
    let idx = +e.detail.activeIndex
    console.log('tabchange', this.data);
    this.setData({
      tabIdx: idx
    })
  },
  // 这个目前没用了 
  getTempFilePath(res) { // 拿到图片临时路径
    if (res.detail) {
      this.setData({
        shareImg: res.detail
      })
    }
  },
  saveImage() {
    const that = this;
    const {
      shareImg
    } = that.data;
    console.log('shareImg', shareImg);
    if (!shareImg) app.showToast('保存图片失败！');
    wx.getSetting({
      success: (res) => {
        console.log(2)
        if (!res.authSetting['scope.writePhotosAlbum']) { //没有授权
          wx.authorize({
            scope: 'scope.writePhotosAlbum',
            success: () => {
              wx.saveImageToPhotosAlbum({
                filePath: shareImg,
                success: function (data) {
                  app.showToast('已保存到相册')
                },
                fail: function (err) {
                  if (err.errMsg === "saveImageToPhotosAlbum:fail auth deny") {
                    that.setData({
                      openSet: true
                    })
                  } else {
                    app.showToast("保存失败，请截屏保存!")
                  }
                },
              })
            },
            fail() {
              // 如果用户拒绝过或没有授权，则再次打开授权窗口
              //（ps：微信api又改了现在只能通过button才能打开授权设置，以前通过openSet就可打开，现在只有打开授权的button弹窗代码）
              that.setData({
                openSet: true
              })
            }
          })
        } else { //有授权
          wx.saveImageToPhotosAlbum({
            filePath: shareImg,
            success: function (data) {
              app.showToast('已保存到相册')
            },
            fail: function (err) {
              if (err.errMsg === "saveImageToPhotosAlbum:fail auth deny") {
                that.setData({
                  openSet: true
                })
              } else {
                app.showToast("保存失败，请截屏保存!")
              }
            },
          })
        }
      }
    })



  },
  submit() {
    this.setData({
      openSet: false
    })
  },
  close() {
    this.setData({
      openSet: false
    })
  }
})



// canvas高度还是不固定的 ，因为条数不固定所以绘制出来的图片也不固定