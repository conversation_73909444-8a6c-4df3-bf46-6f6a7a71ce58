import {
  impact<PERSON><PERSON>,
  clsJson
} from '../../pages/monitoreDetails/common/lFilter/constant'
Component({
  properties: {
    entList: {
      type: <PERSON><PERSON><PERSON>,
      observer(val) {
        console.log(val)
        if (!val?.length) return;
        val.map(i => {
        let arr1 =   (i.impact_name || '').split(',')
        let arr2 = (i.cls_name || '').split(',')
        i.tagAry = [...arr1,...arr2].filter(i=>i) || []
        // console.log( i.tagAry )
          return i;
        })
        this.setData({
          list: val
        })
      }
    },
    time:{
      type:String,
      value:''
    }
  },
  data: {
    list: []
  },

  methods: {
    errorFunction(e) {
      var index = e.currentTarget.dataset.index;
      this.setData({
        [`list[${index}].ent_logo`]: "https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/default.png",
      })
    },
  }
})