var tabBeh = require('../../../template/tabBar/index')
var lFiterBeh = require('../monitoreDetails/common/lFilter/index')
var rFiterBeh = require('../monitoreDetails/common/lFilter/rmixin')

var lbxqMixn = require('../monitoreDetails/common/lFilter/ribaolreqMixin')
var yuqinMixn = require('../monitoreDetails/common/lFilter/ribaorreqMixin')
import {
  getHeight
} from '../../../utils/height'
import {
  daily
} from '../../../service/api'
const app = getApp()
Page({
  behaviors: [
    tabBeh,
    lFiterBeh,
    rFiterBeh,
    lbxqMixn({
      pname: 'lbxq',
      requestFunc: daily.detail
    }),
    yuqinMixn({
      pname: 'yuqin',
      requestFunc: daily.yqdtList
    })
  ],
  data: {
    isFixed: false,
    isDrawing: false, //是否渲染
    login: app.globalData.login,
    contentH: 0, //内容滚动高度
    navigationBarH: app.globalData.navBarHeight, //导航顶部高度
    activeIndex: '0',
    tabs: [{
        title: '风险动态',
        name: 'capital',
      },
      {
        title: '舆情动态',
        name: 'polishing',
      },
    ],
    polishingDropDownMenuTitle: ["风险级别", "风险类别"]
  },
  onLoad(option) {
    app.showLoading('加载中...')
    let {
      created,
      dailyId
    } = option
    this.getHeight()
    // ps后端曹说 舆情动态这里不传GroupId 通过date字段来区分 
    this.setData({
      'lbxqParms.dailyId': dailyId,
      'yuqinParms.date': created,
      'yuqinParms.entTime': created,
      'yuqinParms.startTime': created,
      created: created
    }, () => {
      this.getlbxqList()
      this.getyuqinList()
      setTimeout(() => wx.hideLoading(), 1300)
      this.getHeight()
    })
  },
  onShow() {
    console.log(this.data)
  },
  getHeight() {
    getHeight(this, ['#navbars'], (data) => {
      let {
        screeHeight,
        res,
      } = data
      let navBarH = res[0]?.height //tab栏自身高度 
      let navTopH = res[0]?.top // nab高度 
      let popResH = screeHeight - navBarH
      this.setData({
        navTopH,
        popTopH: navBarH,
        popResH: popResH
      })
    })
  },
  goTop() {
    this.setData({
      scrollTop: this.data.navTopH + 2 //+ 2 //这像素解决的是首页滚动到顶部怕不能触发fixed 导致弹窗和固定定位有空隙就可以滑动的样式问题
    })
  },
  tabClick(e) {
    let {
      title: detail
    } = e.currentTarget.dataset
    if (!detail) return
    let idx = detail == '风险动态' ? 0 : 1;
    this.getTabWidth(idx)
    // 把所有弹窗关闭 
    this.polishingcloseHyFilter()
    this.yuqincloseHyFilter()

    this.setData({
      activeIndex: idx,
      // scrollTop: this.data.navTopH
    }, () => {
      this.getHeight()
    })
  },
  loadMores() {
    this.loadMore(this.data.activeIndex == 0 ? 'lbxq' : 'yuqin')
  },
  onfresherss() {
    this.onfresher(this.data.activeIndex == 0 ? 'lbxq' : 'yuqin')
  },
  // 弹窗相关
  onFlitter(type, obj) { // 筛选
    // console.log(type, obj, '到时根据接口来改')
    let str = type == 'lbxq' ? 'dailyId' : 'date'
    let params = {
      [`${type}IsFlag`]: true, //节流
      [`${type}HasData`]: true, //  是否还有数据
      [`${type}IsNull`]: false,
      [`${type}Parms`]: {
        pageIndex: 1, //偏移量 
        pageSize: 10, //每页多少条
        [str]: this.data[`${type}Parms`]?.[str],
        ...obj
      }
    }
    if (type != 'lbxq') {
      params[`${type}Parms`].entTime = this.data[`${type}Parms`].entTime
      params[`${type}Parms`].startTime = this.data[`${type}Parms`].startTime
    }
    // console.log(type, params, '到时根据接口来改')
    this.setData(params, () => {
      this['get' + type + 'List'](() => {}, true)
    })
  },
})

// 到时需要改得几个点，接口以及接口参数（重置接口参数那些）