import {
  home,
} from '../../../service/api';
import {
  getHeight
} from '../../../utils/height.js'
import {
  collect
} from '../../../utils/mixin/collect'
import {
  getShareUrl
} from '../../../utils/mixin/pageShare'
const app = getApp();
Page({

  /**
   * 页面的初始数据
   */
  data: {
    keyword: '', // 这里的搜索是在发送结果里面搜
    headInfo: { // 头部信息
      name: '',
      release_date: '',
      source: ''
    },
    isTriggered: false,
    recommendIsFlag: true, //推荐企业节流 true允许
    recommendHasData: true, // 推荐企业是否还有数据
    oldReCommendList: [], //原始数据 
    recommendList: [], // 推荐企业列表
    recommendParms: {
      data_source: '',
      id: '',
      theme_type: '',
      type: '',
      page_index: 1, //偏移量
      page_size: 10, //每页多少条
    }, // 推荐企业查询参数
    showContact: false,
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    const list = JSON.parse(decodeURIComponent(wx.getStorageSync('author')))

    if (options?.params) {
      // let data = JSON.parse(decodeURIComponent(options.params))
      this.setData({
        // recommendList: JSON.parse(decodeURIComponent(options.res)),
        recommendList: list.slice(0, 10),
        oldReCommendList: list, //原始数据 
        options: options,
        // headInfo: {
        //   name: data.name,
        //   release_date: data.release_date,
        //   source: data.source
        // },
        template: options?.template || false
      }, () => {
        wx.hideLoading();
      })
    }
    return
    if (islogin && options?.params) {
      let {
        type,
        id,
        data_source,
        name,
        release_date,
        source,
        theme_type
      } = JSON.parse(options.params)
      let params = this.data.recommendParms
      this.setData({
        recommendParms: Object.assign({}, params, {
          type,
          theme_type,
          id,
          data_source
        }),
        headInfo: {
          name,
          release_date,
          source
        }
      })
    } else {
      wx.hideLoading();
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    app.showLoading('加载中');
    this.scrollH()
    const islogin = app.isLogin();
    if (!islogin) {
      app.showToast('查看权威榜单详情请先登录!')
      setTimeout(() => {
        app.route(this, `/pages/login/login`)
      }, 2000)
    }
    // const {
    //   recommendList
    // } = this.data;
    // if (recommendList.length === 0 && islogin) {
    //   app.showLoading('加载中');
    //   this.getRecommendList();
    // }
    this.setData({
      islogin
    }, () => {
      wx.hideLoading();
    });
  },
  scrollH() { // 动态获取列表高度 
    getHeight(this, ['.top'], (data) => {
      const {
        res,
        screeHeight
      } = data
      const height = screeHeight - res[0].height
      this.setData({
        scrollHeight: height
      })
    })
  },
  // 下拉刷新
  refresh() {
    this.setData({
      recommendIsFlag: true, //推荐企业节流 true允许
      recommendHasData: true, // 推荐企业是否还有数据
      recommendList: [], // 推荐企业列表
      'recommendParms.page_index': 1, // 推荐企业查询参数
    });
    app.showLoading('加载中');
    this.getRecommendList();
  },
  // 加载更多
  loadMore() {
    const {
      recommendParms: {
        page_index
      },
      recommendHasData,
      recommendIsFlag
    } = this.data;
    if (!recommendHasData || !recommendIsFlag) return; //节流和没有数据
    this.setData({
      'recommendParms.page_index': page_index + 1
    }, () => this.getRecommendList());
  },
  // 获取推荐企业列表数据
  getRecommendList() {
    let {
      recommendHasData,
      recommendParms,
      recommendList,
      oldReCommendList,
      filteredData,
      keyword
    } = this.data;
    this.setData({
      recommendIsFlag: false
    });
    wx.hideLoading();
    let star = (recommendParms.page_index - 1) * recommendParms.page_size
    let end = (recommendParms.page_index - 1) * recommendParms.page_size + recommendParms.page_size
    let datalist = keyword ? filteredData.slice(star, end) : oldReCommendList.slice(star, end)
    if (datalist?.length < recommendParms.page_size || recommendList.length === oldReCommendList.length) recommendHasData = false;
    this.setData({
      isTriggered: false,
      recommendIsFlag: true,
      recommendHasData,
      recommendList: recommendList.concat(datalist)
    })
    return
    // let {
    //   recommendHasData,
    //   recommendParms,
    //   recommendList
    // } = this.data;
    // this.setData({
    //   recommendIsFlag: false
    // });
    // home.getDefinitiveTop(recommendParms).then(res => {
    //   wx.hideLoading();
    //   let datalist = res?.items?.map(item => {
    //     let {
    //       tags,
    //       fa_ren,
    //       reg_cap,
    //       es_date,
    //       ent_name,
    //       ent_logo,
    //       collected,
    //       ent_id,
    //       location,
    //       region
    //     } = item
    //     tags = tags.filter((tag, ind) => ind < 3);
    //     return {
    //       tags,
    //       legal_person: fa_ren,
    //       register_capital: reg_cap,
    //       register_date: es_date,
    //       ent_name,
    //       logo: ent_logo,
    //       collect: collected,
    //       ent_id,
    //       location,
    //       region
    //     };
    //   })
    //   if (datalist?.length < recommendParms.page_size) recommendHasData = false;
    //   this.setData({
    //     isTriggered: false,
    //     recommendIsFlag: true,
    //     recommendHasData,
    //     recommendList: recommendList.concat(datalist)
    //   })
    // })
  },
  goMap() {
    const {
      locationMap
    } = this.data
    wx.openLocation(locationMap)
  },
  onCloseContact() {
    this.setData({
      showContact: false
    })
  },
  onCloseAddress() {
    this.setData({
      showAddress: false
    })
  },
  onCard(data) {
    let that = this
    app.preventMultipleClick(async () => {
      // 地址  "site"  联系方式 "relation" 官网 "official" 收藏 "collect" 四个字段对呀四个方法逻辑-具体见设计图
      const type = data.detail.type
      const comDetail = data.detail.data
      // 处理收藏 
      if (type == 'collect') {
        collect(that, comDetail, 'recommendList')
      } else if (type == 'relation') {
        this.setData({
          activeEntId: comDetail.ent_id,
          showContact: true
        });
      } else if (type === 'site') {
        this.setData({
          location: {
            lat: +comDetail.location?.lat,
            lon: +comDetail.location?.lon,
          },
          locationTxt: comDetail.register_address,
          addmarkers: [{
            id: 1,
            latitude: +comDetail.location?.lat,
            longitude: +comDetail.location?.lon,
            iconPath: "https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/marker.png",
            width: 20,
            height: 20,
          }],
          showAddress: true,
          locationMap: {
            latitude: +comDetail.location?.lat, //维度
            longitude: +comDetail.location?.lon, //经度
            name: comDetail.register_address, //目的地定位名称
            scale: 15, //缩放比例
            address: comDetail.register_address //导航详细地址
          }
        })
      }
    })
  },
  // 高亮关键词
  highlightKeyword(text, keyword) {
    if (!keyword || !text.includes(keyword)) {
      return [{
        text,
        highlight: false
      }];
    }

    // 将文本分割成匹配和非匹配部分
    const parts = [];
    let lastIndex = 0;
    let index = text.indexOf(keyword);

    while (index !== -1) {
      // 添加非匹配部分
      if (index > lastIndex) {
        parts.push({
          text: text.substring(lastIndex, index),
          highlight: false
        });
      }

      // 添加匹配部分
      parts.push({
        text: text.substring(index, index + keyword.length),
        highlight: true
      });

      lastIndex = index + keyword.length;
      index = text.indexOf(keyword, lastIndex);
    }

    // 添加剩余的非匹配部分
    if (lastIndex < text.length) {
      parts.push({
        text: text.substring(lastIndex),
        highlight: false
      });
    }

    return parts;
  },
  filterRankData(keyword) {
    const {
      oldReCommendList
    } = this.data
    if (!keyword.trim()) {
      // 如果关键词为空，显示所有数据且不高亮
      this.setData({
        filteredData: [],
        recommendList: oldReCommendList.slice(0, 10),
        'recommendParms.page_index': 1
      });
      return;
    }
    // 复制原始数据进行处理
    const filteredData = oldReCommendList.map(i => {
      const isMatched = i.ent_name.includes(keyword);
      if (isMatched) {
        return {
          ...i,
          highlightedTitle: this.highlightKeyword(i.ent_name, keyword),
          matched: true
        }
      }
      return {
        ...i,
        matched: false
      }
    }).filter(i => i.matched)
    this.setData({
      filteredData: filteredData,
      recommendList: filteredData.slice(0, 10),
      'recommendParms.page_index': 1
    })
  },
  handleInput(e) {
    let keyword = e.detail
    if (keyword || keyword == '') {
      this.setData({
        'keyword': keyword,
      });
    }
    //  过滤数组
    this.filterRankData(keyword);
  },
  onShareAppMessage: function () {
    return {
      title: `邀请你查看${this.data.headInfo.name}企业名单`, //自定义转发标题
      path: getShareUrl(`companyPackage/pages/authoritativeList/authoritativeList?template=${this.data.template}`), //分享页面路径
    };
  },
  onUnload() {
    // wx.removeStorageSync('author')
  }
})