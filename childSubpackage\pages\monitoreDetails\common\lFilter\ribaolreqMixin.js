const app = getApp()
module.exports = function (obj) {
  let {
    pname,
    requestFunc
  } = obj
  // 外面需要重新onfresher
  //  behaviors: [ceshiBeh({pname:'',requestFunc:''})], 
  return Behavior({
    data: {
      fresher: true,
      [pname + 'IsFlag']: true, //节流
      [pname + 'HasData']: true, //  是否还有数据
      [pname + 'List']: [], // 列表
      [pname + 'IsNull']: false,
      // 目前这个参数还有点不确定 后续要改
      [pname + 'Parms']: {
        pageIndex: 1, //偏移量 
        pageSize: 10, //每页多少条
        dailyId: '',
      }, // 查询参数
    },
    methods: {
      ['reset' + pname + 'Parms'](isFirst) {
        let params = this.data[`${pname}Parms`]
        this.setData({
          [pname + 'IsFlag']: true, //节流
          [pname + 'HasData']: true, //  是否还有数据
          [pname + 'List']: isFirst ? this.data[`${pname}List`] : [], // 列表
          [pname + 'IsNull']: false,
          [pname + 'Parms']: {
            pageIndex: 1, //偏移量
            pageSize: 10, //每页多少条
            dailyId: this.data[pname + 'Parms'].dailyId
          },
        })
      },
      ['get' + pname + 'List'](callback, isFirst) {
        const that = this;
        let params = that.data[`${pname}Parms`];
        let lists = that.data[`${pname}List`];
        let hasData = that.data[`${pname}HasData`];
        that.setData({
          [pname + 'IsNull']: false,
          [pname + 'IsFlag']: false
        });
        requestFunc(params).then(res => {
          // let {
          //   items: datalist,
          //   count
          // } = res;
          // 这里后续也要根据接口来改 艹
          console.log('ceshi',res)
          let count = res.count,
            datalist = res.datalist || [];
          if (datalist.length < params.pageSize) hasData = false;
          this.setData({
            [pname + 'IsFlag']: true,
            [pname + 'HasData']: hasData,
            [pname + 'List']: isFirst ? [].concat(datalist) : lists.concat(datalist),
            [pname + 'Count']: count ? count : 0
          }, () => {
            if (!that.data[`${pname}List`]?.length) {
              that.setData({
                [pname + 'IsNull']: true
              });
              callback && callback()
              return
            }
            // 目前没用到-预留
            callback && callback()
          })
        })
      },
      loadMore(fields) {
        console.log(fields)
        let field = fields && (typeof fields == 'string') ? fields : pname; //同一页面多个下拉 要区分
        const {
          pageIndex,
          pageSize
        } = this.data[field + 'Parms'];
        const hasData = this.data[field + 'HasData'];
        const isFlag = this.data[field + 'IsFlag'];
        if (!hasData || !isFlag) return; //节流和没有数据
        this.setData({
          [field + 'Parms.pageIndex']: pageIndex + 1
        }, () => {
          let str = 'get' + field + 'List'
          this[str]()
          // console.log(field, this[str])
        });
      },
      async onfresher(fields) {
        let field = fields && (typeof fields == 'string') ? fields : field; //同一页面多个下拉 要区分
        this['reset' + field + 'Parms'](true)
        console.log(`外面根据情况重写下拉刷新调用${field}`)
        try {
          let res = await Promise.all([
            this['get' + field + 'List'](() => {}, true)
          ])
          this.setData({
            fresher: false
          }, () => {
            // this.getHeight()
          })
        } catch (err) {
          console.log(err, '错误提示')
          const url = '/pages/errPage/errPage';
          app.route(this, url)
          return
        }

      }
    }
  })
}