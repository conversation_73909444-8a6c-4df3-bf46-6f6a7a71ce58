<import src="/template/null/null"></import>
<import src="/template/more/more"></import>
<import src="/template/loading/index"></import>
<view class="pages">
  <view>
    <!-- 筛选条件 -->
    <DropDownMenu height="{{filtrateHeight}}" dropDownMenuTitle="{{dropDownMenuTitle}}" class="drop-menu" bindsubmit="onFlitter" heightParams="{{heightParams}}" type="{{ent_name.length =='' && ent_name.length<=0}}" ent_name='{{ent_name}}' bindvip="vipPop" />
    <block>
      <!-- 卡片 -->
      <view wx:if="{{!bazaarIsNull}}">
        <view class="tip" wx:if="{{bazaarlist.length>0}}">
          共找到<text>{{count}}</text>家企业
        </view>
        <scroll-view bindrefresherrefresh="bazaarRefresher" refresher-triggered="{{bazaarIsTriggered}}" refresher-enabled bindscrolltolower="bazaarloadMore" scroll-y style="height: {{cardHeight}}px;background: #f7f7f7;">
          <view class="bus-card">
            <block>
              <block wx:if="{{bazaarlist.length>0}}">
                <block wx:for="{{bazaarlist}}" wx:key="index">
                  <Card bindcardFun='onCard' bindhandleTit="handleTit" obj="{{item}}" bindtap="goto" data-item="{{item}}" />
                </block>
              </block>
              <view wx:else style="height: {{cardHeight}}px;">
                <template is="load"></template>
              </view>
            </block>
            <block>
              <!-- 开通vip -->
              <view class="vip" wx:if="{{permission=='普通VIP'&&bazaarlist.length>=20}}">
                <vipOccupancy bindsubmit="vipPop"></vipOccupancy>
              </view>
              <view wx:elif="{{bazaarlist.length<count}}" style="width: 100%;">
                <template is='more' data="{{hasData:bazaarHasData}}"></template>
              </view>
            </block>
          </view>
        </scroll-view>
      </view>
      <!--暂无数据 -->
      <view wx:if="{{bazaarIsNull}}" style="width: 100%;height: 600rpx;">
        <template is='null'></template>
      </view>
    </block>
  </view>
</view>


<!-- 联系方式弹窗 -->
<Contact visible='{{showContact}}' entId="{{activeEntId}}"></Contact>

<!-- 地址弹窗 -->
<dialog visible='{{showAddress}}' title="地址" isShowConfirm='{{false}}' showFooter="{{false}}">
  <view class="dialog-con">
    <view style="padding: 0 50rpx;">
      <map id="map" longitude="{{location.lon}}" latitude="{{location.lat}}" markers="{{addmarkers}}" scale="{{11}}" style="width: 100%; height: 306rpx;">
      </map>
    </view>
    <view style="margin: 32rpx 0;font-size: 28rpx;">{{locationTxt}}</view>
    <view bindtap="goMap" class="map">
      导航
    </view>
    <view class="cancel" bindtap="onCloseAddress">
      取消
    </view>
  </view>
</dialog>
<!-- vip弹窗 -->
<VipPop visible="{{vipVisible}}"></VipPop>