<!--companyPackage/pages/authoritativeEnter/index.wxml-->
<view class="box">
  <!-- 搜索框 -->
  <view class="search_box">
    <h-input class="search-input" defaultVal="{{keyword}}" placeholder="请输入榜单名称" bindemit="handleInput"></h-input>
  </view>
  <!-- 大卡片 -->
  <scroll-view scroll-y class="list_box">
    <view class="bg_card" wx:for="{{filteredRankTypes}}" wx:key="id" wx:for-item="rankType">
      <view class="bg_tit">
        <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/definitive/type_icon.png"></image>
        <text>{{rankType.name}}</text>
      </view>
      <!-- 列表 -->
      <view class="list">
        <view class="small_cad"
        
         wx:for="{{rankType.lists}}" wx:key="id" data-item="{{item}}">
          <!-- 高亮显示匹配的榜单标题 -->
          <view class="tit">
            <block wx:if="{{keyword && item.highlightedTitle}}">
              <block wx:for="{{item.highlightedTitle}}" wx:for-item="part" wx:key="index">
                <text class="{{part.highlight ? 'highlight' : ''}}">{{part.text}}</text>
              </block>
            </block>
            <text wx:else>{{item.title || '11'}}</text>
          </view>
          <view class="num">{{item.count}}</view>
          <image src="../../image/au_bg.png"></image>
        </view>
      </view>
    </view>
    <!-- 无数据提示 -->
    <view class="no-data" wx:if="{{filteredRankTypes.length === 0 && keyword}}">
      <text>没有找到匹配的榜单</text>
    </view>
  </scroll-view>
</view>