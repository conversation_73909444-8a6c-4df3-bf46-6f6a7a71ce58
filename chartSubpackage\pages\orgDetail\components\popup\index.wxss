/* 外部引入弹窗样式 */
.container_hd {
  /* width: calc(100% - 48rpx); */
  width: 100%;
  height: 192rpx;
  position: absolute;
  left: 0;
  top: 0;
  overflow-y: scroll;
  border-radius: 0rpx 0rpx 16rpx 16rpx;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
}

.z-height {
  width: 100%;
  overflow-y: scroll;
  background: rgba(247, 247, 247, 1);
  border-radius: 16rpx 16rpx 0rpx 0rpx;
  position: absolute;
  bottom: 0;
  left: 0;
}

.sort-list .sort-list__title {
  background-color: #fff;
  padding: 0 60rpx;
  height: 88rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 28rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #20263A;
  border-bottom: 1rpx solid #EEEEEE;
}
.sort-list .sort-list__content {
  padding: 24rpx 0 68rpx 0;
  background-color: #fff;
}
.content-item {
  width: 100%;
  height: 88rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
}
.content-item>view:first-child {
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
}
.content-item .active {
  font-size: 28rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600 !important;
  color: #E72410 !important;
}
.check {
  width: 32rpx;
  height: 32rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAABHklEQVRYR+2WPQ6CQBBGZ6ShUeyUziiF1zAWFjbeRxMttPQutlZewCuQUPnTqZXNOmY1JCSsMAu7Wgg15L35YPYD4ccX/pgPlUCVwP8kcOjU++g4Yz+8rJOb95UE3vDaDhDbSDRrh9dVLGFdIAknorsjaNKKbtuvCOTBpYS1BDhwawJcuBUBHbhxAV24UuDU9Yb0wNCPLpFOUxaBpwTOQWMkADdIeAIBA65EUXhK4NDzpoi4fE1OEHEkysCVr+AYNOcAsOBIlIV//Ag5EibgmVuQJWEKnruGKgkC4cbFojrbdTYnV0DekJIAcmWrmYCzBFISckEUraY7uXYbxkmYhLMTiG3lOeEI2if7vOjk2gmUBX163tr/AFe4EqgSeAJAC/shLUv64wAAAABJRU5ErkJggg==') no-repeat;
  background-size: 100% 100%;
}
.disappear {
  display: none;
}

.show {
  display: block;
}