<import src="/template/tabBar/index"></import>
<import src="/template/null/null"></import>
<import src="/template/more/more"></import>
<import src="../monitoreDetails/common/lFilter/list"></import>
<import src="../monitoreDetails/common/lFilter/rlist"></import>
<import src="../monitoreDetails/common/lFilter/pop"></import>
<view class="daily-page">
  <!-- 导航 -->
  <view style="height: 192rpx;" id="navbars">
    <template is="nav" data="{{activeIndex,tabs,sliderLeft,sliderOffset,sliderWidth}}"></template>
    <view wx:if="{{activeIndex==0}}">
      <template is='lfiter' data="{{dropDownMenuTitle:polishingDropDownMenuTitle,district_open:polishingOne,source_open:polishingTwo,district_val:polishingOne_val,source_val:polishingTwo_val ,filter_open:polishingThree,filter_val:polishingThree_val}}"></template>
    </view>
    <view wx:if="{{activeIndex==1}}">
      <template is='rfiter' data="{{dropDownMenuTitle:yuqinDropDownMenuTitle,district_open:yuqinOne,source_open:yuqinTwo,district_val:yuqinOne_val,source_val:yuqinTwo_val }}"></template>
    </view>
  </view>
  <!-- 内容 -->
  <view>
    <scroll-view class="home" style="height: {{popResH}}px" scroll-y throttle="{{true}}" bindscrolltolower="loadMores" scroll-top="{{scrollTop}}" refresher-triggered="{{fresher}}" refresher-background="#f7f7f7" refresher-default-style="white" refresher-threshold="{{25}}" bindrefresherrefresh="onfresherss" refresher-enabled will-change='transform'>
      <view class="content" style="height: {{popResH}}px">
        <!-- 内容0 -->
        <view class="optimization " wx:if="{{activeIndex == 0}}" style="min-height:calc(100vh - {{popTopH}}px);">
          <view wx:if="{{!lbxqIsNull}}">
            <CardList entList="{{lbxqList}}" time="{{created}}"/>
            <view wx:if="{{lbxqList.length<lbxqCount}}" style="width: 100%;">
              <template is='more' data="{{hasData:lbxqHasData}}"></template>
            </view>
          </view>
          <view style="width: 100%;height:calc(100vh - {{popTopH}}px);" wx:if="{{lbxqIsNull}}">
            <template is='null'></template>
          </view>
        </view>
        <!-- 内容1 -->
        <view class="optimization " wx:if="{{activeIndex == 1}}" style="min-height:calc(100vh - {{popTopH}}px);">
          <view wx:if="{{!yuqinIsNull}}">
            <yuqingList entList="{{yuqinList}}" time="{{created}}"/>
            <view wx:if="{{yuqinList.length<yuqinCount}}" style="width: 100%;">
              <template is='more' data="{{hasData:yuqinHasData}}"></template>
            </view>
          </view>
          <view style="width: 100%;height:calc(100vh - {{popTopH}}px);" wx:if="{{yuqinIsNull}}">
            <template is='null'></template>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>



  <!-- 筛选框--风险级别 -->
  <template is='pop' data="{{popResH:popResH,popTopH:popTopH,isShow:polishingOne, sourceData:polishingOneData, ChooseMethod:'polishingChoose', dataSourceName:'polishingOneData', dataShows:'polishingOne_val'}}"></template>

  <!-- 筛选框--风险类别 -->
  <template is='pop' data="{{popResH:popResH,popTopH:popTopH,isShow:polishingTwo, sourceData:polishingTwoData, ChooseMethod:'polishingChoose', dataSourceName:'polishingTwoData', dataShows:'polishingTwo_val'}}"></template>

  <!-- 筛选框--统计时间 -->
  <template is='pop' data="{{popResH:popResH,popTopH:popTopH,isShow:polishingThree, sourceData:polishingThreeData, ChooseMethod:'polishingChoose', dataSourceName:'polishingThreeData', dataShows:'polishingThree_val'}}"></template>

  <!-- 舆情倾向 -->
  <template is='pop' data="{{popResH:popResH,popTopH:popTopH,isShow:yuqinOne, sourceData:yuqinOneData, ChooseMethod:'yuqinChoose', dataSourceName:'yuqinOneData', dataShows:'yuqinOne_val'}}"></template>
  <!-- 舆情类别 -->
  <template is='pop' data="{{popResH:popResH,popTopH:popTopH,isShow:yuqinTwo, sourceData:yuqinTwoData, ChooseMethod:'yuqinChoose', dataSourceName:'yuqinTwoData', dataShows:'yuqinTwo_val'}}"></template>
</view>