/* companyPackage/pages/authoritativeEnter/index.wxss */
.box {
  height: 100vh;
  background: #f7f7f7;
  display: flex;
  flex-direction: column;
}

.search_box {
  padding: 30rpx 24rpx;
  background: #fff;
  height: 112rpx;
  flex-shrink: 0;
}
.list_box {
  flex: 1;
  overflow-y: scroll;
  box-sizing: border-box;
}
.list_box::-webkit-scrollbar {
  display: none;
  width: 0;
  opacity: 0;
}
.bg_card {
  width: 750rpx;
  background: #ffffff;
  background: #fff;
  padding: 24rpx 20rpx;
  border-top: 24rpx solid #f7f7f7;
}
.bg_tit {
  display: flex;
}
.bg_tit image {
  width: 40rpx;
  height: 40rpx;
  margin-right: 16rpx;
}
.bg_tit text {
  font-weight: 600;
  font-size: 32rpx;
  color: #20263a;
}
.list {
  justify-content: space-between; /* 元素之间平均分布 */
}
.small_cad {
  position: relative;
  display: inline-flex;
  flex-direction: column;
  box-sizing: border-box;
  margin-top: 24rpx;
  width: 218rpx;
  height: 120rpx;
  margin-right: 24rpx;
  padding: 22rpx 20rpx;
}
.small_cad:nth-last-child(3n) {
  margin-right: 0;
}
.small_cad .tit {
  font-weight: 400;
  font-size: 28rpx;
  color: #20263a;
  z-index: 1;
}
.small_cad .num {
  font-weight: 600;
  font-size: 24rpx;
  color: #9b9eac;
  z-index: 1;
}
.small_cad image {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

/* 高亮匹配的文本 */
.highlight {
  color: #ff4d4f;
  font-weight: 600;
}

/* 无数据提示样式 */
.no-data {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  color: #9b9eac;
  font-size: 28rpx;
}
