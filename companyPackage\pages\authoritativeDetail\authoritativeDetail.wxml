<import src="/template/null/null"></import>
<import src="/template/more/more"></import>
<view class="definitive-list">
  <view class="search-wrapper">
    <h-input class="search-input" defaultVal="{{keyword}}" placeholder="榜单名称搜索" bindemit="handleInput"></h-input>
  </view>
  <view class="top">
    <view class="name">{{headInfo.name || '--'}}</view>
    <view class="inline">
      <view class="time publish">发布时间：{{headInfo.release_date || '--'}}</view>
      <view class="time source text-ellipsis">来源：{{headInfo.source || '--'}}</view>
    </view>
  </view>
  <!-- -->
  <scroll-view style="height:{{scrollHeight}}px" refresher-enabled bindrefresherrefresh="refresh" bindscrolltolower="loadMore" scroll-y refresher-background="f7f7f7" refresher-triggered="{{isTriggered}}" class="recommend-wrap {{recommendList.length == 0 ? 'no-margin':''}}" hidden="{{!islogin}}">
    <block wx:if="{{recommendList.length > 0}}">
      <block wx:for="{{recommendList}}" wx:key="ent_id">
        <Card bindcardFun='onCard' index="{{index}}" isBangDan="{{true}}" obj="{{item}}" bindtap="goto" data-item="{{item}}" />
      </block>
      <view wx:if="{{recommendList.length>=recommendParms.page_size}}" style="width: 100%;">
        <template is='more' data="{{hasData:recommendHasData}}"></template>
      </view>
    </block>
    <!--暂无数据-->
    <view wx:else style="height:{{scrollHeight}}px;width: 100%">
      <template is='null'></template>
    </view>
  </scroll-view>
  <view class="card-login" hidden="{{islogin}}">
    <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/null.png" mode="aspectFit"></image>
    <view class="txt">未登录的用户暂时无法使用推荐功能</view>
  </view>
</view>
<!-- 联系方式弹窗 -->
<Contact visible='{{showContact}}' entId="{{activeEntId}}"></Contact>
<!-- 地址弹窗 -->
<dialog visible='{{showAddress}}' title="地址" isShowConfirm='{{false}}' showFooter="{{false}}">
  <view class="dialog-con">
    <view style="padding: 0 50rpx;">
      <map id="map" longitude="{{location.lon}}" latitude="{{location.lat}}" markers="{{addmarkers}}" scale="{{11}}" style="width: 100%; height: 306rpx;">
      </map>
    </view>
    <view style="margin: 32rpx 0;font-size: 28rpx;" class="text-ellipsis">{{locationTxt}}</view>
    <view bindtap="goMap" class="map">
      导航
    </view>
    <view class="cancel" bindtap="onCloseAddress">
      取消
    </view>
  </view>
</dialog>