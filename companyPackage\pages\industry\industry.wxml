<!--
目前 导航和搜索框都是公用的 -- 搜索功能没做（不知道是跳转还是根据关键字发送请求--是否模糊查询）
 -->
<view class="page">
    <!-- 导航 -->
    <view class="industyr-nav">
        <block wx:for="{{tabs}}" wx:key="*this">
            <view id="{{index}}" class="industyr-nav__item {{activeIndex == index ? 'industyr-nav__item_on' : ''}}"
                bindtap="onTabClick">
                <view class="industyr-navr__title">{{item.title}}</view>
            </view>
        </block>
    </view>
    <!-- input框 -到时候加一个图标 -或者图片 -->
    <input type="text" class="industry-input" placeholder-class="iconfont icon-soushuo" placeholder="" />
    <!-- tage -->
    <view class="industy-tag">
        <block wx:for="{{tag}}" wx:key="index">
            <view class="industy-tag-item {{tagActiveIdx == index ? 'industy-tag-item_on' : ''}}" bindtap="onTagClick"
                id='{{index}}'>
                {{item.name}}</view>
        </block>
    </view>
    <!-- 卡片 -->
    <view class="ceshi">
        <PageScroll currentHeight="{{scrollHeight}}">
            <view class="industry-card" style="padding-bottom: {{statusHeight}}px; ">
                <block wx:for="{{6}}" wx:key='index'>
                    <view class="industry-card-item">
                        <view class="card-item-t">
                            2020中国生物医药企业TOP20
                        </view>
                        <view class="card-item-n">
                            <view class="card-item-n-i">
                                <view>图标</view>
                                <view class="card-item-space"></view>
                                <view>生物医药</view>
                            </view>
                            <view class="card-item-n-i">
                                <view>来源:</view>
                                <view class="card-item-space"></view>
                                <view>中国生物产业网</view>
                            </view>
                        </view>
                    </view>
                </block>
            </view>
        </PageScroll>
    </view>

</view>