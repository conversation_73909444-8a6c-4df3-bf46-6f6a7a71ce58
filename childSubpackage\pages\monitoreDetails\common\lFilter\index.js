import {
  hijack
} from '../../../../../utils/route';
import {
  formatterParams
} from '../../../../../utils/util';
import {
  risk_level,
  risk_type,
  jiankongData,
  formatDate
} from '../../../../../utils/formate'
const app = getApp()
module.exports = Behavior({
  data: {
    polishingDropDownMenuTitle: ['风险级别', '风险类别', '统计时间'],
    polishingOne: false, //1
    polishingTwo: false, // 2
    polishingThree: false, // 3
    polishingFour: false, //4
    // 判断是否选项框有值--高亮---这三个值外面也要用到
    polishingOne_val: false,
    polishingTwo_val: false,
    polishingThree_val: false,
    polishingFour_val: false,
    // 
    polishingShownavindex: -1,
    polishingOneData: {
      code: 'riskLevel',
      curVal: '',
      children: JSON.parse(JSON.stringify(risk_level))
    },
    polishingTwoData: {
      code: 'riskCls',
      curVal: '',
      children: JSON.parse(JSON.stringify(risk_type))
    },
    polishingThreeData: {
      code: 'time',
      curVal: '', //默认九十天
      children: JSON.parse(JSON.stringify(jiankongData))
    },
  },

  methods: {

    polishingChoose(e) {
      let {
        item,
        source,
        shows
      } = e.currentTarget.dataset
      let soucreData = this.data[source],
        show_val;
      soucreData.children.forEach(i => {
        i.show = false
        if (i.code == item.code) {
          i.show = !item.show
        }
      })
      if (item.show) {
        soucreData.curVal = ''
        show_val = false
      } else {
        soucreData.curVal = item.code
        show_val = true
      }
      this.setData({
        [source]: soucreData,
        [shows]: show_val
      })
      this.polishingcloseHyFilter()
      this.polishingBackAll()
    },

    polishingBackAll() {
      // 处理数据 发请求 scope_code 
      const {
        polishingOneData,
        polishingTwoData,
        polishingThreeData
      } = this.data
      let str = '',
        obj = {};
      if (polishingOneData.curVal) {
        obj[polishingOneData.code] = polishingOneData.curVal
      } else {
        delete obj[polishingOneData.code]
      }

      if (!polishingTwoData.curVal) {
        delete obj[polishingTwoData.code]
      } else {
        obj[polishingTwoData.code] = polishingTwoData.curVal
      }

      if (polishingThreeData.curVal) {
        let now = new Date()
        let startTime = formatDate(Date.now() - (polishingThreeData.curVal * 24 * 60 * 60 * 1000), 'yyyy-MM-dd')
        let endTime = formatDate(now, 'yyyy-MM-dd')
        obj['startTime'] = startTime
        obj['endTime'] = endTime
      } else {
        delete obj['startTime']
        delete obj['endTime']
      }
      // console.log(obj)
      this.onFlitter('lbxq', obj)
    },





    /**关闭筛选*/
    polishingcloseHyFilter: function (e) {
      if (e && e.target.dataset['type'] && e.target.dataset['type'] == 'child') return;
      if (this.data.polishingOne) {
        this.setData({
          polishingOne: false,
          polishingTwo: false,
          polishingThree: false,
          polishingFour: false,
          polishingShownavindex: -1
        })
      } else if (this.data.polishingTwo) {
        this.setData({
          polishingTwo: false,
          polishingOne: false,
          polishingThree: false,
          polishingFour: false,
          polishingShownavindex: -1
        })
      } else if (this.data.polishingThree) {
        this.setData({
          polishingTwo: false,
          polishingOne: false,
          polishingThree: false,
          polishingFour: false,
          polishingShownavindex: -1
        })
      } else if (this.data.polishingFour) {
        this.setData({
          polishingTwo: false,
          polishingOne: false,
          polishingThree: false,
          polishingFour: false,
          polishingShownavindex: -1
        })
      }
    },
    polishingtapDistrictNav: hijack(function (e) {
      if (this.data.polishingShownavindex != -1) {
        this.polishingclickNav(this.data.polishingShownavindex)
        this.setData({
          polishingShownavindex: -1
        })
        this.polishingcloseHyFilter()
        return
      }
      if (this.data.polishingOne) {
        this.setData({
          polishingOne: false,
          polishingTwo: false,
          polishingThree: false,
          polishingFour: false,
          polishingShownavindex: 0
        })
      } else {
        this.setData({
          polishingOne: true,
          polishingTwo: false,
          polishingThree: false,
          polishingFour: false,
          polishingShownavindex: e.currentTarget.dataset.nav
        })
      }
    }, {
      type: 'searchs-btn',
      app: app
    }),
    polishingtapSourceNav: hijack(function (e) {
      if (this.data.polishingShownavindex != -1) {
        this.polishingclickNav(this.data.polishingShownavindex)
        this.setData({
          polishingShownavindex: -1
        })
        this.polishingcloseHyFilter()
        return
      }
      if (this.data.polishingTwo) {
        this.setData({
          polishingTwo: false,
          style_open: false,
          polishingOne: false,
          polishingThree: false,
          polishingFour: false,
          polishingShownavindex: 0
        })
      } else {
        this.setData({
          polishingTwo: true,
          style_open: false,
          polishingOne: false,
          polishingThree: false,
          polishingFour: false,
          polishingShownavindex: e.currentTarget.dataset.nav
        })
      }
    }, {
      type: 'searchs-btn',
      app: app
    }),
    tapFilterNav: hijack(function (e) {
      if (this.data.polishingShownavindex != -1) {
        this.polishingclickNav(this.data.polishingShownavindex)
        this.setData({
          polishingShownavindex: -1
        })
        this.polishingcloseHyFilter()
        return
      }
      if (this.data.polishingThree) {
        this.setData({
          polishingTwo: false,
          polishingOne: false,
          polishingThree: false,
          polishingFour: false,
          polishingShownavindex: 0
        })
      } else {
        this.setData({
          polishingTwo: false,
          polishingOne: false,
          polishingThree: true,
          polishingFour: false,
          polishingShownavindex: e.currentTarget.dataset.nav
        })
      }
    }, {
      type: 'searchs-btn',
      app: app
    }),
    polishingfourFilterNav: hijack(function (e) {
      if (this.data.polishingShownavindex != -1) {
        this.polishingclickNav(this.data.polishingShownavindex)
        this.setData({
          polishingShownavindex: -1
        })
        this.polishingcloseHyFilter()
        return
      }
      if (this.data.polishingFour) {
        this.setData({
          polishingTwo: false,
          polishingOne: false,
          polishingThree: false,
          polishingFour: false,
          polishingShownavindex: 0
        })
      } else {
        this.setData({
          polishingTwo: false,
          polishingOne: false,
          polishingThree: false,
          polishingFour: true,
          polishingShownavindex: e.currentTarget.dataset.nav
        })
      }
    }, {
      type: 'searchs-btn',
      app: app
    }),
    //点击Nav切换发送请求
    polishingclickNav(index) {
      // switch (index) {
      //   case '1':
      //     this.opportunitySure()
      //     break;
      //   case '2':

      //     break;
      //   case '3':
      //     this.canyeSure()
      //     break;

      //   default:
      //     break;
      // }
    },
  },
})