var echarts = require('../../../../ec-canvas/echarts')
import geoJson from './mapData.js';
import { getPx } from '../../../../../utils/formate'
const CHINA_CODE = '1000000';

const app = getApp();
let myChart1 = null;

Component({
  properties: {
    region: {
      type: Object,
      observer(val) {
        this.getMapData(val);
      }
    },
    heatMapData: {
      type: Array,
      observer(val) {
        this.setData({
          hotMapData: val ? val : []
        }, () => {
          this.initMap()
        })
      }
    },
  },
  data: {
    ec1: {
      lazyLoad: true
    },
    forceUseOldCanvas: false,
    map: 'china',
    currentCode: CHINA_CODE,
    mapJson: {},
    chinaMap: {},
    hotMapData: []
  },
  lifetimes: {
    attached() {
      wx.getSystemInfo({
        success: (res) => res.platform == 'devtools' && this.setData({ forceUseOldCanvas: true }),
      })
      this.getMapData({
        code: CHINA_CODE
      });
    }
  },
  methods: {
    getMapData(region) {
      const that = this;
      let {
        currentCode,
        mapJson,
        chinaMap,
        map
      } = that.data;
      map = region?.code === CHINA_CODE ? 'china' : region.name;
      const isLevel3 = region.level === '3';
      currentCode = region?.code == 'ALL' ? CHINA_CODE : region.code;
      if (isLevel3) {
        currentCode = region.parent;
      }
      wx.request({
        url: `https://cdn.qiyedata.net/opensource/map_json_1.0/json/${currentCode}.json`,
        method: 'GET',
        success: (res) => {
          mapJson = res.data;
          if (isLevel3) {
            mapJson = {
              type: res.data.type,
              features: res.data.features.filter(item => region.code.includes(String(item.properties.adcode))),
            };
          }
          if (currentCode === CHINA_CODE) {
            chinaMap = res.data;
          }
          echarts.registerMap(currentCode === CHINA_CODE ? 'china' : 'custom', mapJson); // 绘制中国地图
          this.setData({
            currentCode,
            mapJson,
            chinaMap,
            map
          }, () => {
            this.initMap({
              geo: {
                map: currentCode === CHINA_CODE ? 'china' : 'custom'
              }
            });
          })
        }
      })
    },
    //中国地图
    initMap(options = {}) {
      let {mapJson, hotMapData } = this.data
      if (!mapJson.type) return;
      this.chart1Componnet = this.selectComponent('#mychart1');
      this.chart1Componnet.init((canvas, width, height, dpr) => {
        myChart1 = echarts.init(canvas, null, {
          width: width,
          height: height,
          devicePixelRatio: dpr
        });
        const option = {
          tooltip: {
            trigger: 'item',
            formatter: params => {
              return `${params.name} ${isNaN(params.value) ? 0 : params.value}`;
            },
          },
          // 地理坐标系组件
          geo: [this.getGeo(options?.geo)],
          //图例
          visualMap: [],
          series: []
        }
        const json = mapJson.features.map(item => {
          return {
            ...item,
            ...item.properties,
          };
        });
        // 添加热力图
        const data = this.mergeArray(hotMapData, json).sort((a, b) => a.value - b.value);
        const seriesIndex = option.series.length;
        option.visualMap.push(this.getVisualMap(data, {
          seriesIndex
        }));
        option.series[seriesIndex] = {
          name: '',
          type: 'map',
          data: data,
          geoIndex: 0,
          selectedMode: false,
        };
        myChart1.setOption(option);
        return myChart1;
      });
    },
    getGeo(options = {}) {
      let {  currentCode  } = this.data
      return {
        type: "map", //图表类型
        map: currentCode === CHINA_CODE ? 'china' : 'custom', //,
        roam: false, // 可以缩放和平移
        aspectScale: 0.8, // 比例
        layoutCenter: ["50%", "48%"], // position位置
        layoutSize: 322, // 地图大小，保证了不超过 370x370 的区域
        label: {
          show: false,
          emphasis: {
            show: false,
            textStyle: {
              color: '#fff',
            },
          },
        },

        itemStyle: {
          // 图形上的地图区域
          normal: {
            areaColor: '#fff',
            borderColor: '#389dff',
            borderWidth: 0.5,
          },
          emphasis: {
            areaColor: '#2e317c',
            shadowOffsetX: 0,
            shadowOffsetY: 0,
            borderWidth: 0,
            // shadowBlur: 20,
            // shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        }
      }
    },
    mergeArray(ary1, ary2) {
      const result = [];
      for (let i = 0; i < ary2.length; i++) {
        const item1 = ary2[i];
        const find = ary1.find(item => item.name === item1.name);
        const res = find || {
          name: item1.name,
          value: 0
        };
        result.push(Object.assign(item1, res));
      }
      return result;
    },
    getVisualMap(data, options) {
      const min = data[0].value || 0;
      const max = data[data.length - 1].value || 0;
      return {
        show: true,
        left: '0',
        bottom: '0',
        max,
        min,
        // z: 999,
        calculable: false,
        itemWidth: getPx(32),
        itemHeight: getPx(140),
        align: 'right',
        textGap: getPx(8),
        text: ['高', '低'], // 文本，默认为数值文本
        inRange: {
          color: ['#ECF8FF','#388BE9'],
          symbolSize: [30, 100]
        },
        textStyle: {
          color: '#282D30',
        },
        seriesIndex: 0,
        ...options,
      };
    }
  }

})