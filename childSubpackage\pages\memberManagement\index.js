import { getHeight } from '../../../utils/height';
import { getOrgData, invitation, deleteUser } from '../../../service/user'
const app  = getApp();
Page({
  data: {
    textArr: ['分享链接给微信好友', '好友点击链接填写信息后提交申请', '审核通过好友申请', '成功加入团队'],
    accountDetail: '',
    subAccount: []
  },
  onLoad() {
    const this_ = this;
    const { org_id, user_id } = wx.getStorageSync('userData')
    this.setData({
      org_id, user_id
    }, () =>{
      this_.getOrgDatas();
    });
  },
  onReady() {
    this.getHeight();
  },
  onShow() {
    this.getOrgDatas();
  },
  getHeight() {
    const that = this;
    getHeight(that, ['.header',], (data) => {
      let { res, screeHeight  } = data;
      let cardHeight = screeHeight - res[0]?.top - res[0]?.height - 50;
      that.setData({ cardHeight })
    })
  },
  async getOrgDatas(){
    wx.showLoading()
    const { org_id } = this.data;
    const data = await getOrgData(org_id);
    this.setData({
      subAccount: data?.sub_account_user_data,
      accountDetail: {
        admin_user:data?.admin_user_data?.user_name || '',
        current: data?.current_user_num || 0,
        limit: data?.user_limit || 0
      }
    });
    wx.hideLoading()
  },
  invitationUser(){
    const { org_id, user_id } = this.data;
    invitation({ org_id, user_id }).then((res) => {
      const { invitation_url } = res;
      this.setData({ invitation_url })
    });
  },
  // 邀请成员
  addUser(){
    const this_ = this;
    this.setData({ addVisible: true }, () => {
      this_.invitationUser();
    })
  },
  addClose(){
    this.setData({ addVisible: false })
  },
  addSubmit(){
    const { invitation_url } = this.data;
    console.log('invitation_url', invitation_url);
    if(invitation_url) {
      wx.setClipboardData({
        data: invitation_url,
        success: function (res) {
          wx.showToast({
            title: '邀请链接复制成功'
          })
        }
      })
    }
    this.setData({ addVisible: false })
  },

  // 删除成员
  handleDelete({ detail }){
    this.setData({ deleteVisible: true, deleteUserId: detail })
  },
  deleteClose(){
    const { deleteUserId } = this.data;
    this.setData({ deleteVisible: false })
    const url = `/childSubpackage/pages/projectManagement/index?track_user_id=${deleteUserId}`;
    app.route(this, url)
  },
  deleteSubmit(){
    const { deleteUserId } = this.data;
    deleteUser(deleteUserId)
    wx.showToast({
      title: '删除成功',
    })
    this.setData({ deleteVisible: false })
    this.getOrgDatas()
  },

  // 成员审核
  checkUser(){
   const url = `/childSubpackage/pages/memberManagement/chechUser/index`;
   app.route(this, url)
  },
})