.wrap {
  height: 100vh;
  overflow-y: scroll;
  scroll-behavior: smooth;
  background: #fff;
}

.topImg {
  width: 100%;
  height: 244rpx;
}

.iptbox {
  display: flex;
  align-items: center;
  margin: 8rpx 24rpx 48rpx;
  background: #F4F4F4;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  padding: 18rpx 24rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #9B9EAC;
}

.iptbox .img {
  width: 36rpx;
  height: 36rpx;
  margin-right: 16rpx;
}

.list {
  display: flex;
  justify-content: space-between;
  padding: 0 24rpx;
  flex-wrap: wrap;
}

.list .item {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 24rpx 16rpx 20rpx 20rpx;
  width: 340rpx;
  height: 160rpx;
  background: #FFFFFF;
  border-radius: 8rpx 8rpx 8rpx 8rpx;
  opacity: 1;
  border: 1rpx solid #EEEEEE;
  margin-bottom: 20rpx;
}

.list .item view:nth-of-type(1) {
  font-size: 28rpx;
  font-family: PingFang SC-Semibold, PingFang SC;
  font-weight: 600;
  color: #3E414D;
}

.list .item view:nth-of-type(2) {
  width: 32rpx;
  height: 32rpx;
  background: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAYAAABzenr0AAAAAXNSR0IArs4c6QAAAPJJREFUWEft1jEKwkAQBdAZsLDxEMk2XiMnsLDxEILewCto4S0ELe1ykmQuYSl8iSAEgmFnMmsQknIJ+98Ms8syjfzxyPk0Af6/AyIyB3Bl5nuWZSftTA3uQFVVa2a+NMHMvNciBgOaYBHZAThaEC6AIQg3gBXhCrAg3AFaRBKABpEMEIvoAETkoL1M+v4HsCGi5bcj2gHUdQ1PQHsvAI8QwqK99tMOENE2z/NzL8Cz+pgbMtkQxoS/58Kz4s9eseFJAJpwd4A23BVgCXcDWMNdACKyAnCzPEa8AOO+CZsqyrKcFUXxtBzpJPeABjIBpg68AKpdqSFrkGg5AAAAAElFTkSuQmCC') no-repeat;
  background-size: 100% 100%;
}

.list .item image {
  position: absolute;
  width: 78rpx;
  height: 100rpx;
  bottom: 0;
  right: 16rpx;
}