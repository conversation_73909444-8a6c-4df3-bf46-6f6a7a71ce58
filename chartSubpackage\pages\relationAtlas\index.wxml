<view class="relation">
    <view class="head">
        <view class="bgImg">
            <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/rel_r_bg.png" mode="aspectFit"></image>
        </view>
        <view class="rel-wrap">
            <view class="title">目标主体</view>
            <!-- 表单 -->
            <view class="rel-wrap-add">
                <block wx:for="{{related_args}}" wx:key="index">
                    <view class="add-item">
                        <view class="add-item-l" bindtap="handle" data-type="{{related_args.length <= 1?'add':index==related_args.length-1 ?index==9?'edit': 'add':index==0?'edit':'delet'}}" data-index="{{index}}">
                            <view class="icon  {{related_args.length <= 1?'add':index==related_args.length-1 ?index==9?'edit': 'add':index==0?'edit':'delet'}}">
                            </view>
                            <!-- 通过100%,50%和top:2来控制 -->
                            <view class="line {{related_args.length <= 1 ?'none':index==0?'moreHead':index==related_args.length-1?'moreEnd':'' }}">
                                <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/line.png" mode="aspectFill"></image>
                            </view>
                        </view>
                        <input class="add-item-r" placeholder="添加人员或企业" placeholder-class="pla" disabled bindtap="addVal" data-index="{{index}}" value="{{item.name?item.name:item.ent_name ? item.ent_name:''}}" />
                    </view>
                </block>

            </view>
            <!-- 下钻层级 -->
            <view class="tier">
                <view>下钻层级</view>
                <view class="slider">
                    <!-- 这里的圆没办法变成其他颜色  min="{{1}}" //为了渲染不能加这个-->
                    <slider bindchange="onSliderChange" max="{{9}}" step="1" activeColor="#E72410" backgroundColor="#f2f2f2" block-size="25" value="{{sliderVal}}" block-color="#E72410" class="source-slider" />
                    <!-- 原来的样式满足不了所以写一个覆盖掉 -->
                    <view class="slider-item">
                        <view class='slider-line'>
                            <view class="slider-line-b">
                                <!-- 实际控制这个进度条宽高 -->
                                <view class="slider-active" style="width: {{(sliderVal)/9*100}}\%;">
                                    <view class="circle slider-active-block "></view>
                                </view>
                            </view>
                        </view>
                    </view>
                </view>
                <view>{{sliderVal+1}}层</view>
            </view>
            <!-- 按钮 -->
            <view class="btn {{!isFlag&&'actives'}}" bindtap="crateAtlas">
                生成图谱
            </view>
            <!-- 更多关系探寻 -->
            <view class="more-relation" bindtap="goBackRelation"> 返回基础关系探寻 </view>
        </view>
    </view>
    <!-- 历史导出记录 -->
    <view class="history" wx:if="{{hisList.length>0}}">
        <view class="title">历史导出记录</view>
        <view class="history-list">
          <block wx:for="{{hisList}}" wx:key="index">
            <view class="history-item" data-id="{{item.id}}" wx:if="{{ index < 5}}">
              <view class="text-l text-ellipsis">{{item.description}}</view>
              <view
                class="text-r text-r-1 flex_all_center"
                wx:if="{{item.export_status === 'EXPORTED'}}"
                bindtap="saveImage"
                data-url="{{item.download_url}}"
              >
                <view>下载图片 </view>
              </view>
              <view class="text-r text-r-2 flex_all_center" wx:if="{{item.export_status === 'EXPORTING'}}">
                <view>导出中</view>
              </view>
              <view
                class="text-r text-r-3 flex_all_center"
                wx:if="{{item.export_status === 'EXPORT_FAILED'}}"
                bindtap="recallDownload"
                data-item="{{item}}"
              >
                <view>重新导出</view>
              </view>
            </view>
            </block>
        </view>
        <view class="show-more" bindtap="getMoreHistory" wx:if="{{ hisList.length > 5}}"> 查看全部 </view>
    </view>
    <!-- dialog -->
    <Dialog wx:if="{{showDialog}}" bindsubmit="submit" bindclose="close"></Dialog>
     <!-- 图片保存权限 dialog -->
    <Dialog wx:if="{{openSet}}" bindsubmit="openSetSubmit" bindclose="openSetClose"></Dialog>
  <!-- 开通vip -->
  <view class="vip" wx:if="{{ vipShow }}">
    <vipOccupancy bindsubmit="vipPop"></vipOccupancy>
  </view>
   <!-- vip弹窗 -->
   <VipPop visible="{{vipVisible}}"></VipPop>
</view>