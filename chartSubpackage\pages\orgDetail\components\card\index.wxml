<view class="fin-card">
  <view class="fin-card-l">
    <image src="{{objData.picture_url||' '}}" binderror="errorFunction" />
  </view>
  <!-- 右边  -->
  <view class="fin-card-r">
    <view class="card_h">
      <view class="card_h_l" bindtap="clickTitle" data-item="{{ objData }}" wx:if="{{objData.com_name.length }}">
        <view wx:for="{{ objData.com_name }}" wx:key="index" class="text {{ item == objData.heightKey && 'activetext' }}">{{ item }}</view>
      </view>
      <view class="card_h_l" bindtap="clickTitle" data-item="{{ objData }}" wx:else="">
        <view class="text">---</view>
      </view>
      <view class="card_h_r">
        <text>{{objData.invest_date}}</text>
      </view>
    </view>
    <!-- 标签 -->
    <view class="card_tag">
      <view class="card_tag_box">
        <!-- tags -->
        <text class="card_tag_i card_tag_i_1">{{ objData.invest_tag }}</text>
        <text class="card_tag_i card_tag_i_2"> {{ objData.catTag }}</text>
      </view>
    </view>
    <!-- 其它模块 -->
    <view class="card_con">
      <view class="card_con_i">
        <view>合投机构：</view>
        <view class="org_name" wx:if="{{ objData.investment.length }}">
          <block wx:for="{{objData.investment }}" wx:key="index" data-item="{{item}}">
            <text bindtap="clickOrg" data-item="{{item}}">{{ item.org_name }} </text>
            <text class="line"> | </text>
          </block>
        </view>
        <view wx:else> 未透露 </view>
      </view>
    </view>
  </view>
</view>