<!--components/RegionSelection/RegionSelection.wxml-->
<HalfScreenPop visible="{{visible}}" position="bottom" title="行业筛选" bindsubmit="close" _maskClosable="{{true}}"
    showCancelCls showCancelBtn="{{false}}" confirmBtnText="取消" disableAnimation footHeigh="168rpx" zIndex="{{99}}">
    <view slot="customContent" class="area">
        <view class="region-wrap">
            <!-- 省 -->
            <scroll-view scroll-y="{{true}}" class="list province-wrap">
                <view wx:for="{{regionList}}" wx:key="code" wx:for-item="province">
                    <view class="item items {{province.active && 'actived'}}" data-code="{{province.code}}"
                        data-level="{{province.level}}" bindtap="changeRegion" data-is_leaf="{{province.is_leaf}}">
                        <text>{{province.name}}</text>
                    </view>
                </view>
            </scroll-view>
            <!-- 市-->
            <scroll-view scroll-y="{{true}}" class="list city-wrap" wx:if="{{provinceList.length}}">
                <view wx:key="code" wx:for-item="city" wx:for="{{provinceList}}">
                    <view class="item {{city.active && 'actived'}}" data-code="{{city.code}}"
                        data-level="{{city.level}}" bindtap="changeRegion" data-is_leaf="{{city.is_leaf}}">
                        <text>{{city.name}}</text>
                    </view>
                </view>
            </scroll-view>
            <!-- 区  -->
            <scroll-view scroll-y="{{true}}" class="area-wrap list" wx:if="{{cityList.length}}">
                <view wx:key="code" wx:for-item="area" wx:for="{{cityList}}">
                    <view class="item {{area.active && 'actived'}}" data-code="{{area.code}}"
                        data-level="{{area.level}}" data-parent="{{area.parent}}" bindtap="getArea">
                        <text>{{area.name}}</text>
                    </view>
                </view>
            </scroll-view>
        </view>
    </view>
</HalfScreenPop>