/* 表格 */
.table {
  width: 100%;
  height: auto;
  border: 2rpx solid #EEEEEE;
}
.table>view {
  width: 100%;
  min-height: 80rpx;
  display: flex;
  justify-content: space-between;
}
.table>view>text {
  display: inline-block;
  text-align: center;
  padding: 20rpx 38rpx;
}
.table_th {
  background: #F7F7F7;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
}
.table_th>text:first-child {
  flex: 1;
  border-right: 2rpx solid #EEEEEE;
}
.table_th>text:last-child {
  /* flex: 1; */
  width: 304rpx;
}

.table_content {
  width:100%;
  height: calc(100% - 84rpx);
}
.table_td {
  min-height: 80rpx;
  display: flex;
  justify-content: space-between;
}
.table_td>text {
  display: inline-block;
  text-align: center;
  padding: 20rpx 38rpx;
}

.table_td>text:first-child {
  /* width: 132rpx; */
  flex: 1;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #20263A;
  border-top: 2rpx solid #EEEEEE;
  border-right: 2rpx solid #EEEEEE;
}
.table_td>text:last-child {
  width: 304rpx;
  font-size: 28rpx;
  font-family: PingFang SC-Regular, PingFang SC;
  font-weight: 400;
  color: #1E75DB;
   border-top: 2rpx solid #EEEEEE;
}
.table>view:last-child {
  border-bottom: 2rpx solid #EEEEEE;
}