import {
    setTagColor
} from '../../../../../../utils/util.js'
const app = getApp()
Component({

    options: {
        addGlobalClass: true,
    },
    /**
     * 组件的属性列表
     */

    properties: {
        souceList: { //传进来的数组 ，渲染 -目前是写死的 --通过setTagColor处理一下标签
            type: A<PERSON><PERSON>,
            observer(newVal) {
                this.setData({
                    souceData: newVal
                })
            }
        },
        hasData: {
          type: Boolean,
          value: true
        },
        pageSize:{
          type: Number,
          value: 10
        },
        permission: {
          type: String,
          value: ''
        },
        isLogin: {
          type: Boolean,
          value: false
        },
        chain: {
          type: Array,
          value: []
        }
    },

    /**
     * 组件的初始数据
     */
    data: {
        souceData: [],
    },

    /**
     * 组件的方法列表
     */
    methods: {
        login() {
          const { chain } = this.data;
          const url = `/companyPackage/pages/industryChain/chainList/chainList?chainName=${chain[1]}&chain_code=${chain[0]}`;
          app.route(this, `/pages/login/login?url=${url}`);
        },
        vipPop(val) {
          this.triggerEvent('vipPop', val)
        },
        // 收藏
        collect(e) {
            const item = e.target.dataset['item'] || e.currentTarget.dataset['item']
            const index = e.target.dataset['index'] || e.currentTarget.dataset['index']
            console.log("点击收藏", index);
            this.triggerEvent('cardFun', {
                type: 'collect',
                index,
                data: item
            })
        },
        // 联系方式
        relation(e) {
            const item = e.target.dataset['item'] || e.currentTarget.dataset['item']
            this.triggerEvent('cardFun', {
                type: 'relation',
                data: item
            })
        },
        // 官网 
        official(e) {
            const item = e.target.dataset['item'] || e.currentTarget.dataset['item']
            if (item.official_website) {
                wx.setClipboardData({
                    data: item.official_website,
                    success(res) {
                        wx.showToast({
                            title: '复制成功',
                            icon: 'none'
                        })
                    }
                })
            } else {
                wx.showToast({
                    title: '该企业暂无官网',
                    icon: 'none'
                })
            }
        },
        // 发地址 
        site(e) {
            const item = e.target.dataset['item'] || e.currentTarget.dataset['item']
            console.log(item)
            this.triggerEvent('cardFun', {
                type: 'site',
                data: item
            })
        },
        loadMore() {
            this.triggerEvent('loadMore')
        },
        handleRefresher() {
            this.triggerEvent('refresh')
        },
        goDetail(e) {
            let { ent_id } = e.currentTarget.dataset.item;
            console.log("跳转id", ent_id);
            if (ent_id) {
                // const url = `/subPackage/pages/companyDetail/companyDetail?id=${ent_id}`;
                // app.route(this, url)
                const url = encodeURIComponent(`https://reporth5.handidit.com?entId=${ent_id}`);
                app.route(this, `/subPackage/pages/webs/index?url=${url}`);
                this.triggerEvent('handleTit');
            }

        }
    }
})