import { debounce } from '../../../utils/formate';
import { publish } from "../../../service/api";
import { getHeight } from '../../../utils/height';
import { onScroll } from "../../../utils/util";
const { getAllManual } = publish;
const app = getApp()
Page({
  data: {
    // input
    inputShowed: false, //是否聚焦
    ent_name: "", //搜索值
    ents_name: "",
    // list
    operationList: [], // 操作列表
    searchList: [], // 通过搜索查询说明
    cacheId: null,
    intoView: "",
  },
  onLoad(props) {
    const { id } = props;
    app.showLoading();
    this.setData({ cacheId: id }, () => this.initData(true));
  },
  async initData(toView = false) {
    await this.getAllManualClassList();
    wx.hideLoading();
    this.getRestHeight();
    toView && this.toComment(`#id${this.data.cacheId}`);
  },
  // 获取操作说明列表
  getAllManualClassList(cb) {
    return getAllManual().then(({ class_list = [] }) => {
      this.setData({ operationList: class_list });
    });
  },
  getRestHeight() {
    return getHeight(this, ['.searbox'], (data) => {
      let { screeHeight, res } = data;
      this.setData({
        contentH: screeHeight - res[0].height - 20, //内容滚动高度
      });
    });
  },
  onHandlerShow(event) {
    const { item, index } = event.currentTarget.dataset;
    item._show = !item._show;
    this.setData({ [`operationList[${index}]`]: item });
  },
  onHandlerClick(event) {
    const { item: { id } = {} } = event.currentTarget.dataset;
    const url = `/childSubpackage/pages/updaetDyDetail/index?id=${id}&page=operation&title=操作说明`;
    app.route(this, url);
  },
  onfresher() {
    console.log("调用");
    this.initData().then(() => {
      this.setData({ fresher: false });
    })
  },
  // 
  onBlur() {
    // 拿到ent_name --传入最近搜索历史 
    const ent_nameue = this.data.ent_name
    if (ent_nameue.trim().length > 0) {
      // console.log(222)
      // 
    } else {
      this.setData({
        inputShowed: false
      });
    };

  },
  onInput: debounce(function ([...e]) {
    let keyword = e[0].detail.value;
    if (keyword || keyword == '') {
      this.setData({ ent_name: keyword }, () => {
        console.log('keyword', keyword)
        this.inputQuest(keyword)
      });
    }
  }, 200),
  onClear() {
    this.unLocked()
    this.setData({
      ent_name: '',
      ents_name: '',
    })
    // this.inputQuest('suspend')
  },
  unLocked() {
    wx.hideKeyboard();
    this.setData({
      loading: false,
      inputShowed: false
    })
  },
  inputQuest(keyword) {
    let searchList = [];
    const { operationList } = this.data;
    if (!keyword || !keyword.trim()) {
      this.setData({ searchList });
      return false;
    }
    // 遍历key
    operationList.forEach(item => {
      const results = item.res_vo_list.reduce((quence, entry) => {
        const { manual_title } = entry;
        const exp = new RegExp(`(${keyword})`);
        const result = exp.exec(manual_title);
        if (result) {
          let split = [];
          const { index } = result;
          split = [manual_title.substr(0, index), manual_title.substr(index, keyword.length), manual_title.substr(index + keyword.length)];
          return [...quence, {
            textNodes: split.map((str) => ({ text: str, high: str === keyword })),
            ...entry,
          }];
        }
        return quence;
      }, []);
      searchList.push(...results);
    });
    this.setData({ searchList })
  },
  goBack() {
    this.unLocked()
    // this.init()
    app.route(this, null, 'navigateBack')
  },
  toComment(id) {
    var query = wx.createSelectorQuery()//创建节点查询器
    query.select(id).boundingClientRect()//选择id为comment的节点并查询的它布局位置
    query.select(".searbox").boundingClientRect()//选择id为comment的节点并查询的它布局位置
    query.exec((res) => {
      wx.pageScrollTo({ scrollTop: res[0].top });
      this.setData({ intoView: res[0].top - res[1].height - 10 });
    })
  },
})