var echarts = require('../../../../ec-canvas/echarts')
const app = getApp();
var chart = null;
// 将16进制颜色转换成rgb
function hexToRgb(hex, opacity) {
  if (opacity !== 0) {
    opacity = opacity || 1;
  }
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex);
  if (result) {
    const r = parseInt(result[1], 16);
    const g = parseInt(result[2], 16);
    const b = parseInt(result[3], 16);
    return `rgba(${r},${g},${b},${opacity})`;
  }
  return hex;
}

Component({
  properties: {
    type: {
      type: String,
      default: 'line',
    }, // line 类型 ，控制是否填充 ，默认填充，可选
    unit: String, // y轴名称
    legend: Boolean, // 是否加载legend
    legendStyle: Object, // 图例样式
    stack: <PERSON>olean, // 是否堆叠
    showYName: {
      type: Boolean,
      default: false,
    }, // 是否显示y轴名称
    data: {
      type: Object,
      observer: function (val) {
        const { legend } = val;
        if (legend?.length) {
          this.chain_option(val);
        }
      }
    },
  },
  lifetimes: {
    attached() {
      wx.getSystemInfo({
        success: (res) => res.platform == 'devtools' && this.setData({ forceUseOldCanvas: true }),
      })
    },
  },
  data: {
    ec: {
      lazyLoad: true, // 初始化图表
    },
    forceUseOldCanvas: false,
  },
  methods: {
    chain_option(data) {
      this.chart1Componnet = this.selectComponent('#mychart-dom-line');
      this.chart1Componnet.init((canvas, width, height, dpr) => {
        chart = echarts.init(canvas, null, {
          width: width,
          height: height,
          devicePixelRatio: dpr
        });
        const color = ['#4AB8FF', '#26C8A7', '#4995C6', '#FFB93E', '#9C85DB'];
        const { value, legend } = data;
        const type = this.data.type;
        const option = this.setOption();
        // if (!this.data.stack) {
        //   const dataConfig = this.setSeries(color[0], type);
        //   dataConfig.data = value;
        //   option.series = [dataConfig];
        // } else {
        option.series = value.map((item, index) => {
          const dataConfig = this.setSeries(color[(index)], type);
          return { ...item, ...dataConfig };
        });
        if (this.data.legend) {
          option.legend.show = true;
        }
        // }
        const unit = this.data.unit ? this.data.unit : '亿元';
        option.xAxis.data = legend;
        this.data.showYName && (option.yAxis.name = `单位：${unit}`);
        chart.clear()
        // console.log(option);
        chart.setOption(option, true);
        return chart;
      });

    },
    drawOff() {
      chart.clear();
    },
    setSeries(color, type) {
      const { subValue } = this.data;
      const config = {
        type: 'line',
        showAllSymbol: true,
        symbol: 'circle',
        symbolSize: 1,
        label: {
          formatter: (params) => {
            const { dataIndex, value, data } = params;
            if (subValue?.length) {
              return subValue[dataIndex] + '%';
            } else if (data.subValue) {
              return data.subValue + '%';
            } else {
              return value;
            }
          },
          emphasis: {
            color: color,
            show: true,
          },
        },
        lineStyle: {
          color: color,
          width: 3,
        },
        itemStyle: {
          normal: {
            color: color,
            borderWidth: 0,
          },
          emphasis: {
            borderWidth: 5,
            borderColor: hexToRgb(color, 0.4),
          },
        },
      };

      if (!type || type !== 'line') {
        config.areaStyle = {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: hexToRgb(color, 0.6), // 0% 处的颜色
              },
              {
                offset: 0.5,
                color: hexToRgb(color, 0.2), // 100% 处的颜色
              },
              {
                offset: 1,
                color: hexToRgb(color, 0), // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        };
      }
      return config;
    },
    setOption() {
      const textColor = { color: '#20263A' }; // 文字颜色
      const xAxis = {
        type: 'category',
        boundaryGap: true,
        axisTick: { show: true, alignWithLabel: true },
        axisLabel: {
          ...textColor,
        },
        axisLine: {
          lineStyle: {
            color: '#EEEEEE',
          },
        },
        axisLabel: {//x轴文字的配置
          show: true,
          textStyle: {
            color: '#20263A',
            fontWeight: 400,
          }
        },
      };
      const yAxis = {
        type: 'value',
        name: "单位： ",
        nameTextStyle: {
          ...textColor,
          // padding: [0, 0, 10, 0],
        },
        axisLabel: {
          ...textColor,
        },
        splitLine: {
          lineStyle: {
            type: 'dashed',
            color: '#DEDEDE',
          },
        },
      };
      const option = {
        grid: {
          left: 0,
          top: 30,
          right: 0,
          bottom: 0,
          containLabel: true,
        },
        tooltip: {
          show: true,
          trigger: 'axis',
          confine: true,
          axisPointer: {
            z: -1,
            lineStyle: {
              color: '#DEDEDE',
            },
          },
          // formatter: (params) => {
          //   const _dom = `<div class="tooltip">
          //     <h3>${params[0].axisValue}</h3>
          //     ${params.map((item, index) => {
          //     const className = index === 0 ? 'source' : 'target';
          //     return `<p class="${className}">${item.seriesName} <span>${item.value}${this.unit}</span></p>`;
          //   }).join('')}
          // </div>`;
          //   return _dom;
          // },
        },
        legend: {
          show: false,
          itemGap: 20,
          bottom: 0,
          itemWidth: 10,
          itemHeight: 0,
          ...this.legendStyle,
        },
        // dataZoom: [
        //   {
        //     type: 'inside',
        //     start: 0,
        //     end: 30,
        //     xAxisIndex: [0],
        //   }
        // ],
        xAxis,
        yAxis,
      };
      return option;
    },
    drawChart(data) {
      const color = ['#4AB8FF', '#26C8A7', '#4995C6', '#FFB93E', '#9C85DB'];
      const { value, legend } = data;
      const type = this.type;
      const myChart = this.$echarts.init(document.getElementById(this.id));
      const option = this.setOption();
      if (!this.stack) {
        const dataConfig = this.setSeries(color[0], type);
        dataConfig.data = value;
        option.series = [dataConfig];
      } else {
        option.series = value.map((item, index) => {
          const dataConfig = this.setSeries(color[(index)], type);
          return { ...item, ...dataConfig };
        });
        if (this.legend) {
          option.legend.show = true;
        }
      }
      const unit = this.unit ? this.unit : '亿元';
      option.xAxis.data = legend;
      this.showYName && (option.yAxis.name = `单位：${unit}`);
      myChart.setOption(option, true);
    },
  }


});
