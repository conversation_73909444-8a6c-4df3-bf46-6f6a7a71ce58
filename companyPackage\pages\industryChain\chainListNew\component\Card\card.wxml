<scroll-view refresher-enabled bindrefresherrefresh="handleRefresher" refresher-triggered="{{isTriggered}}" class="list" style="height: {{height}}px" bindscrolltolower="loadMore" scroll-y="{{true}}">
  <view class="search-card-wrap" wx:if="{{souceData.length}}">
    <view class="search-card" wx:for="{{souceData}}" wx:key="index" data-item="{{item}}" bindtap="goDetail">
      <view class="card-boxs">
        <view class="card-logo">
          <image src="{{item.ent_logo || 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/chain_logo.png'}}" mode="aspectFit"></image>
        </view>
        <view class="card-head">
          <view class="card_h">
            <view class="card_h_l text-ellipsis" data-item="{{item}}">
              <!-- {{item.ent_name}} -->
              <text class="{{ v.type === 'HIGH'?'text-high':'' }}" wx:for="{{item.label}}" wx:key="text" wx:for-item="v">{{ v.text }}</text>
            </view>
            <view class="card_h_r" catchtap="collect" data-item="{{item}}" data-index="{{index}}">
              <!-- 后面根据具体传进来的字段判断 -->
              <view class="card_h_r_img">
                <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>" wx:if="{{!item.collect}}"></image>
                <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>" wx:else></image>
              </view>
            </view>
          </view>
          <view class="card_tag">
            <view class="card_tag_box">
              <text class="{{tagItem.tagColor}}" wx:for="{{item.tags}}" wx:for-item="tagItem" wx:key="tagName" wx:if="{{tagItem.tagName&&tagItem.tagName!='-'}}">{{tagItem.tagName}}</text>
            </view>
          </view>
        </view>
      </view>
      <view class="card_c">
        <view class="card_c_i card_c_is">
          <text class="name">法人代表人</text>
          <text class="cont contblue">{{item.legal_person}}</text>
        </view>
        <view class="card_c_i card_c_is">
          <text class="name">注册资本</text>
          <text class="cont">{{item.reg_cap}}万</text>
        </view>
        <view class="card_c_i">
          <text class="name">成立日期</text>
          <text class="cont">{{item.es_date}}</text>
        </view>
      </view>
      <view class="card_ico">
        <view class="card_ico_i" catchtap="relation" data-item="{{item}}">
          <view class="card_ico_i_img">
            <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"></image>
          </view>
          <view>
            联系方式
          </view>
        </view>
        <view wx:if="{{item.official_website}}" class="card_ico_i" catchtap="official" data-item="{{item}}">
          <view class="card_ico_i_img">
            <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"></image>
          </view>
          <view>
            官网
          </view>
        </view>
        <view class="card_ico_i" catchtap="site" data-item="{{item}}">
          <view class="card_ico_i_img">
            <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"></image>
          </view>
          <view>
            地址
          </view>
        </view>
      </view>
    </view>
  </view>
  <!-- 未登录只能查看5条数据 -->
  <view class="vip" wx:if="{{!isLogin && souceData.length >= 5}}">
    <vloginOverlay bindsubmit="login"></vloginOverlay>
  </view>
  <!-- 开通vip -->
  <view class="vip" wx:if="{{permission=='普通VIP'&&souceData.length>=20}}">
    <vipOccupancy bindsubmit="vipPop"></vipOccupancy>
  </view>
  <view wx:elif="{{souceData.length>=pageSize}}" style="width: 100%;">
    <template is='more' data="{{hasData}}"></template>
  </view>
</scroll-view>