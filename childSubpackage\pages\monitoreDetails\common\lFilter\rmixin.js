import {
  hijack
} from '../../../../../utils/route';
import {
  impactJson,
  clsJson
} from './constant'
const app = getApp()
module.exports = Behavior({
  data: {
    yuqinDropDownMenuTitle: ['舆情倾向', '舆情类别'],
    yuqinOne: false, //1
    yuqinTwo: false, // 2
    yuqinThree: false, // 3
    yuqinFour: false, //4
    // 判断是否选项框有值--高亮---这三个值外面也要用到
    yuqinOne_val: false,
    yuqinTwo_val: false,
    yuqinThree_val: false,
    yuqinFour_val: false,
    // 
    yuqinShownavindex: -1,
    yuqinOneData: {
      code: 'impact',
      curVal: '',
      children: impactJson
    },
    yuqinTwoData: {
      code: 'cls',
      curVal: '',
      children: clsJson
    },
  },

  methods: {

    yuqinChoose(e) {
      let {
        item,
        source,
        shows
      } = e.currentTarget.dataset
      let soucreData = this.data[source],
        show_val;
      soucreData.children.forEach(i => {
        i.show = false
        if (i.code == item.code) {
          i.show = !item.show
        }
      })
      if (item.show) {
        soucreData.curVal = ''
        show_val = false
      } else {
        soucreData.curVal = item.code
        show_val = true
      }
      this.setData({
        [source]: soucreData,
        [shows]: show_val
      })
      this.yuqincloseHyFilter()
      this.yuqinBackAll()
    },

    yuqinBackAll() {
      // 处理数据 发请求 scope_code 
      const {
        yuqinOneData,
        yuqinTwoData,
        yuqinThreeData
      } = this.data
      let str = '',
        obj = {};
      if (yuqinOneData.curVal) {
        obj[yuqinOneData.code] = yuqinOneData.curVal
      } else {
        delete obj[yuqinOneData.code]
      }

      if (!yuqinTwoData.curVal) {
        delete obj[yuqinTwoData.code]
      } else {
        obj[yuqinTwoData.code] = yuqinTwoData.curVal
      }
      // console.log(obj)
      this.onFlitter('yuqin', obj)
    },





    /**关闭筛选*/
    yuqincloseHyFilter: function (e) {
      if (e && e.target.dataset['type'] && e.target.dataset['type'] == 'child') return;
      if (this.data.yuqinOne) {
        this.setData({
          yuqinOne: false,
          yuqinTwo: false,
          yuqinThree: false,
          yuqinFour: false,
          yuqinShownavindex: -1
        })
      } else if (this.data.yuqinTwo) {
        this.setData({
          yuqinTwo: false,
          yuqinOne: false,
          yuqinThree: false,
          yuqinFour: false,
          yuqinShownavindex: -1
        })
      } else if (this.data.yuqinThree) {
        this.setData({
          yuqinTwo: false,
          yuqinOne: false,
          yuqinThree: false,
          yuqinFour: false,
          yuqinShownavindex: -1
        })
      } else if (this.data.yuqinFour) {
        this.setData({
          yuqinTwo: false,
          yuqinOne: false,
          yuqinThree: false,
          yuqinFour: false,
          yuqinShownavindex: -1
        })
      }
    },
    yuqintapDistrictNav: hijack(function (e) {
      if (this.data.yuqinShownavindex != -1) {
        this.yuqinclickNav(this.data.yuqinShownavindex)
        this.setData({
          yuqinShownavindex: -1
        })
        this.yuqincloseHyFilter()
        return
      }
      if (this.data.yuqinOne) {
        this.setData({
          yuqinOne: false,
          yuqinTwo: false,
          yuqinThree: false,
          yuqinFour: false,
          yuqinShownavindex: 0
        })
      } else {
        this.setData({
          yuqinOne: true,
          yuqinTwo: false,
          yuqinThree: false,
          yuqinFour: false,
          yuqinShownavindex: e.currentTarget.dataset.nav
        })
      }
    }, {
      type: 'searchs-btn',
      app: app
    }),
    yuqintapSourceNav: hijack(function (e) {
      if (this.data.yuqinShownavindex != -1) {
        this.yuqinclickNav(this.data.yuqinShownavindex)
        this.setData({
          yuqinShownavindex: -1
        })
        this.yuqincloseHyFilter()
        return
      }
      if (this.data.yuqinTwo) {
        this.setData({
          yuqinTwo: false,
          style_open: false,
          yuqinOne: false,
          yuqinThree: false,
          yuqinFour: false,
          yuqinShownavindex: 0
        })
      } else {
        this.setData({
          yuqinTwo: true,
          style_open: false,
          yuqinOne: false,
          yuqinThree: false,
          yuqinFour: false,
          yuqinShownavindex: e.currentTarget.dataset.nav
        })
      }
    }, {
      type: 'searchs-btn',
      app: app
    }),
    //点击Nav切换发送请求
    yuqinclickNav(index) {
      // switch (index) {
      //   case '1':
      //     this.opportunitySure()
      //     break;
      //   case '2':

      //     break;
      //   case '3':
      //     this.canyeSure()
      //     break;

      //   default:
      //     break;
      // }
    },
  },
})