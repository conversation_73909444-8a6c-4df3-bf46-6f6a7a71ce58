Component({
  properties: {
    entList: {
      type: <PERSON><PERSON><PERSON>,
      observer(val) {
        this.setData({
          list: val || []
        })
      }
    },
    time:{
      type:String,
      value:''
    }
  },
  data: {
    list: []
  },

  methods: {
    errorFunction(e) {
      var index = e.currentTarget.dataset.index;
      this.setData({
        [`list[${index}].ent_logo`]: "https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/default.png",
      })
    },
  }
})