<view class="page">
  <!-- input框 -->
  <view class="page_tit" bindtap="goSearch">
    <view class="left">
      <image src="../../image/address.png" class="img1"></image>
      <text>{{centerLocationName}}</text>
    </view>
    <image src="../../image/r_arrow.png" class="img2"></image>
  </view>
  <!-- <view class="merch">
    <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/search.png"></image>
    <input class="merch-input" placeholder="附近企业中搜索" placeholder-class="placeholder_class" confirm-type="search" bindconfirm="searchContent" value="{{searchVal}}" />
  </view> -->
  <view class="br_line"></view>
  <!-- 筛选组件 -->
  <view class="merch-menu">
    <DownMenu isLocation="{{isLocation}}" dropDownMenuTitle="{{['行业','距离','更多筛选']}}" bindslider="onSliderChange" bindindustry="onIndustryChange" bindsearch="onSearchChange" hideClass="{{hideClass}}" bindvip="vipPop"></DownMenu>
  </view>
  <!-- 滚动区域  -->
  <movable-area class="move_box">
    <!-- 地图 -->
    <map id="map" longitude="{{longitude}}" show-location bindregionchange="regionchange" bindscale="onMapScale" latitude="{{latitude}}" scale="{{scale}}" markers="{{markers}}" circles="{{circles}}" style="width: 100%; height: 668rpx;">
      <view style="position: absolute; left: 50%; top: 50%; transform: translate(-50%,-100%);width: 80rpx;height: 80rpx;">
        <image src="../../image/center_ico.png"></image>
      </view>
      <view style="position: absolute; right: 3%; bottom: 10%; width: 80rpx;height: 80rpx;" bindtap="backToOriginalCenter">
        <image src="../../image/back_ico.png"></image>
      </view>
    </map>
    <movable-view class="move_view" inertia direction="vertical" bindtouchend="touchend" bindtouchstart="touchstart" animation="{{true}}" y="{{y}}" damping="40">
      <view class="merchbox {{ hideClass ? 'no_radius' : '' }}">
        <view class="touch_bar {{ hideClass ? 'no_show' : '' }}"></view>
        <view class="touch_txt {{ hideClass ? 'no_margin' : '' }}">
          附近找到
          <text style="color: #E72410;font-weight: bold;">{{count}}</text>
          家相关企业
        </view>
        <!-- 卡片 -->
        <view>
          <Card emptyBoxHeight="{{emptyBoxHeight}}" wx:if="{{isLocation}}" isLogin="{{isLogin}}" souceList="{{requestData}}" searchVal="{{searchVal}}" bindcardFun="onCard" bind:bindscroll="bindscroll" bind:loadMore="loadMore"></Card>
        </view>
      </view>
      <view class="btn-wrap">
        <button wx:if="{{!isLocation}}" class="btns" bindtap="handleAuth" style="font-weight:600;">
          授权
        </button>
      </view>
    </movable-view>
  </movable-area>
  <Contact visible="{{showContact}}" entId="{{activeEntId}}"></Contact>
  <!-- 地址弹窗 -->
  <dialog visible="{{showAddress}}" title="地址" isShowConfirm="{{false}}" showFooter="{{false}}">
    <view class="dialog-con">
      <view style="padding: 0 50rpx;">
        <map id="map" longitude="{{location.lon}}" latitude="{{location.lat}}" markers="{{addmarkers}}" scale="{{11}}" style="width: 100%; height: 306rpx;">
        </map>
      </view>
      <view style="margin: 32rpx 0;font-size: 28rpx;">{{locationTxt}}</view>
      <view bindtap="goMap" class="map"> 导航 </view>
      <view class="cancel" bindtap="onCloseAddress"> 取消 </view>
    </view>
  </dialog>
  <VipPop visible="{{vipVisible}}"></VipPop>
</view>