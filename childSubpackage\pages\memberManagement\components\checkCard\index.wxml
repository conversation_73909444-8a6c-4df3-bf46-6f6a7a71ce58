
<view class="sub_card">
  <view class="title">
    <view> <view class="icon1" />加入申请</view>
    <view class="icon" bindtap="delete"/>
  </view>
  <view class="name">{{ data.user_name }}</view>
  <view class="phone_box">
    <view class="phone"> {{ data.phone }} </view>
    <view class="status" wx:if="{{ data.status === 1}}">
      <text class="btn refused" bindtap="handleRefused"> 拒绝</text>
      <text class="btn consent" bindtap="handleConsent"> 同意</text>
    </view>
    <view class="text" wx:else>{{data.status === 2 ?'已同意' : '已拒绝'}}</view>
  </view>
</view>