<import src="/template/null/null.wxml" />
<view class="pages">
  <view class="page_head">
    <DropDownMenu defaultChainVal="{{defaultChainVal}}" dropDownMenuTitle="{{dropDownMenuTitle}}" class="drop-menu" bindsubmit="onFlitter" bindvip="vipPop" />
    <view class="company_num">共找到 <text class="color_num">{{company_num}}</text> 家企业</view>
  </view>
  <!-- 企业列表  -->
  <view>
    <block wx:if="{{requestData.length>0}}">
      <view class="card-box" style="height: {{computedHeight}}px;">
        <Card 
          souceList="{{requestData}}" 
          isLogin="{{isLogin}}" 
          permission="{{permission}}" 
          hasData="{{hasData}}" 
          chain="{{[chain_codes[0], dropDownMenuTitle[1]]}}"
          bindvipPop="vipPop" 
          bindcardFun='onCard' 
          bindloadMore='loadMore' 
          bindhandleTit="handleTit"
          bindrefresh='handleRefresher'
        ></Card>
      </view>
    </block>
    <block wx:if="{{requestData.length <= 0 }}">
      <view class="queshen" style="height: 100vh;">
        <template is="null" />
      </view>
    </block>
  </view>

  <Contact visible='{{showContact}}' entId="{{activeEntId}}"></Contact>
  <!-- 地址弹窗 -->
  <dialog visible='{{showAddress}}' title="地址" isShowConfirm='{{false}}' showFooter="{{false}}">
    <view class="dialog-con">
      <view style="padding: 0 50rpx;">
        <map id="map" longitude="{{location.lon}}" latitude="{{location.lat}}" markers="{{addmarkers}}" scale="{{11}}" style="width: 100%; height: 306rpx;">
        </map>
      </view>
      <view style="margin: 32rpx 0;font-size: 28rpx;">{{locationTxt}}</view>
      <view bindtap="goMap" class="map">
        导航
      </view>
      <view class="cancel" bindtap="onCloseAddress">
        取消
      </view>
    </view>
  </dialog>
  <!-- 地区弹窗 --后面几个渲染都一样，把这个做出来（确定好）后续的根据不同传不同参数regionPop -->
  <multiplec-choice visible="{{regionPop}}" dataType="districtAry" bindsubmit="submitSub" oldData="{{params.regionData}}"></multiplec-choice>
  <!-- 行業 -->
  <multiplec-choice visible="{{eleseicPop}}" dataType="eleseicAry" bindsubmit="eleseicSub" oldData="{{params.eleseic_data}}"></multiplec-choice>
  <!-- 企業类型 -->
  <multiplec-choice visible="{{enttypePop}}" dataType="enttypeAry" bindsubmit="enttypeSub" oldData="{{params.enttype_data}}"></multiplec-choice>
  <!-- 企业许可 -->
  <multiplec-choice visible="{{districtPop}}" dataType="allCertAry" bindsubmit="allCertSub" oldData="{{params.all_cert_data}}"></multiplec-choice>
  <!-- 最高年限   dateType="{{dateType}}" _date="{{date}}" bindsetDate="setDate" changePadding="{{true}}"-->
  <DatePicker visible="{{datePop}}" _date="{{date}}" bindsetDate="setDate" changePadding="{{true}}"></DatePicker>
  <!-- vip弹窗 -->
  <VipPop visible="{{vipVisible}}"></VipPop>
  <HalfScreenPop title="提示" position="bottom" zIndex="{{99}}" showCancelBtn="{{false}}" showCancelCls visible="{{isExpPop}}" confirmBtnText="知道了">
    <view class="expPop" slot="customContent">
      根据不同招商场景，对企业规模进行的等级划分，大型、特大型企业适合政府园区招商；中小型、中型企业适合民营园区招商；微型、小微型企业可能存在高成长性企业
    </view>
  </HalfScreenPop>
</view>