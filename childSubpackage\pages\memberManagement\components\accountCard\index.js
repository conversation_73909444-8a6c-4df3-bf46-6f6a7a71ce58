import { deleteUser } from '../../../../../service/user'
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    item: {
      type: Object,
      value: {},
      observer(val){
        this.setData({ data: val })
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    delete(){
      const { data: { user_id } } = this.data;
      this.triggerEvent('delete', user_id);
    }
  }
})
