const app = getApp();
// 引入高德地图微信小程序SDK
var amapFile = require('../../lib/amap-wx130.js');

module.exports = function (params) {
  return Behavior({
    data: {
      markers: [], //实际渲染的markers
      // 圈内markers集合
      insideMarkes: [],
      // 圈外markers
      outMarkes: [],
      circles: [],
      mapCtx: null,
      // 防抖相关
      _debounceTimer: null,
      _isUpdating: false,

      // 中心点管理
      originalCenter: {
        latitude: '',
        longitude: '',
        locationName: '当前位置'
      }, // 原始中心点（功能：用户点击回到当前位置）
      currentCenter: {
        latitude: '',
        longitude: '',
        locationName: ''
      }, // 当前中心点（拖动或缩放后的中心点）--请求以及渲染

      // 业务回调函数
      _onCenterChange: null, // 中心点变化回调
      _onDataRefresh: null // 数据刷新回调
    },

    lifetimes: {
      attached() {},

      ready() {
        if (!this.mapCtx) {
          this.mapCtx = wx.createMapContext('map');
        }
      },

      detached() {
        this._cleanup();
      }
    },

    methods: {
      /**
       * 初始化地图
       */
      _initializeMap() {
        if (!this.mapCtx) {
          this.mapCtx = wx.createMapContext('map');
        }
        if (!this.myAmapFun) {
          // 初始化高德地图SDK
          this.myAmapFun = new amapFile.AMapWX({
            key: 'ea478181d97e3d92be0150c05603ddb6'
          });
        }
      },

      // 初始化定位服务
      _initializeLocation() {
        // 直接获取当前位置，更可靠
        this.getCurrentLocation((latitude, longitude, name) => {
          // 本地有就用本地
          const mapSearchItem = wx.getStorageSync('mapSearchItem');
          if (mapSearchItem) {
            const item = JSON.parse(mapSearchItem);
            this.updateCurrentCenter(
              item.location.lat,
              item.location.lng,
              item.title
            );
            wx.removeStorageSync('mapSearchItem');
            return;
          }
          // 本地没有就用传进来的经纬度
          this.updateCurrentCenter(latitude, longitude, name);
        });
      },
      // 获取当前位置
      getCurrentLocation(callback) {
        const that = this;
        this.myAmapFun.getRegeo({
          success: function (data) {
            const latitude = data[0]?.latitude;
            const longitude = data[0]?.longitude;
            const name = data[0]?.name;
            // 设置原始中心点
            that.setOriginalCenter(latitude, longitude, name);
            // 将经纬度抛出去 用于请求
            callback && callback(latitude, longitude, name);
          },
          fail: function (info) {
            const {
              originalCenter
            } = that.data;
            if (originalCenter.latitude) {
              callback &&
                callback(
                  originalCenter.latitude,
                  originalCenter.longitude,
                  originalCenter.name
                );
              return;
            }
            // 如果获取位置失败，使用默认位置（北京）
            callback &&
              callback(
                39.9042,
                116.4074,
                '北京市东城区东华门街道北京市人民政府'
              );
          }
        });
        // wx.getLocation 这个小程序不能一致掉
      },
      // 更新当前中心点
      updateCurrentCenter(latitude, longitude, name) {
        if (!latitude || !longitude || !name) return;

        // 将地图移动到当前中心点
        this.setMapCenter(latitude, longitude);
        // 更新圆圈
        this.updateCircle(latitude, longitude);
        // 首先设置当前中心点以及名称
        const currentCenter = {
          latitude,
          longitude,
          locationName: name
        };
        // 设置相关参数
        this.setData({
            currentCenter,
            'params.page_index': 1,
            'params.grids': [{
              lon: longitude,
              lat: latitude
            }],
            hasData: true,
            requestData: []
          },
          () => {
            // 请求列表
            this.getHot();
            this.getList();
            // this.generateMockMarkers();
          }
        );
      },
      /**
       * 清理资源
       */
      _cleanup() {
        // 清除防抖定时器
        if (this._debounceTimer) {
          clearTimeout(this._debounceTimer);
          this._debounceTimer = null;
        }
      },
      // 获取数据生成marks
      async getHot() {
        // 逻辑 1.中心经纬度==后端(确定区县)=》获取两份数据： 一份是圈内数据 ，一份是圈外数据（市下---》区县名称以及经纬度）
        // 2.将数据保存起来 判断当前缩放程度决定渲染那套markers
        // 监听缩放，当缩放到一定程度展示不停markers 大于区县就圈外数据，小于区县就圈内数据
        const centerLat = this.data.currentCenter.latitude;
        const centerLng = this.data.currentCenter.longitude;
        const radius = this.data.params.radius; // 圆圈半径（米）
        // mock数据-------------------------------------------------
        const mockMarkers = [];
        // 生成10-20个随机标记点
        const markerCount = 5; // 10-20个
        for (let i = 0; i < markerCount; i++) {
          const randomPoint = this.generateRandomPointInCircle(
            centerLat,
            centerLng,
            radius
          );
          mockMarkers.push(randomPoint);
        }
        // ------------------------------处理mock数据
        // 圈内mark
        const insideMarkes = [...mockMarkers].map((i, idx) => {
          return {
            ...i,
            id: idx,
            iconPath: '../../image/m_ico.png', // 替换为你的图标路径
            width: 18,
            height: 24,
            callout: {
              content: `北部区(等${idx + 1}家)`, // Use name if available, otherwise default
              color: '#fff',
              fontSize: 13,
              display: 'ALWAYS',
              borderRadius: 4,
              padding: 6,
              bgColor: '#F03F2D',
              textAlign: 'center',
              anchorY: '-3'
            },
            label: {
              content: '5',
              color: '#fff',
              fontSize: 12,
              bgColor: 'transparent',
              anchorX: '0',
              anchorY: '-26',
              textAlign: 'center',
              width: 18,
              height: 24
            }
          };
        });
        // 圈外mark
        const outMarkes = [{
            id: 0,
            width: 0,
            height: 0,
            latitude: 29.6,
            longitude: 106.6,
            alpha: 0,
            label: {
              content: `渝北区(等1家)`, // Use name if available, otherwise default
              color: '#fff',
              fontSize: 13,
              display: 'ALWAYS',
              borderRadius: 4,
              padding: 6,
              bgColor: '#F03F2D',
              textAlign: 'center',
              anchorY: '-40'
            }
          },
          {
            id: 1,
            width: 0,
            height: 0,
            latitude: 29.75,
            longitude: 106.2,
            alpha: 0,
            label: {
              content: `合川区(等1家)`, // Use name if available, otherwise default
              color: '#fff',
              fontSize: 13,
              display: 'ALWAYS',
              borderRadius: 4,
              padding: 6,
              bgColor: '#F03F2D',
              textAlign: 'center',
              anchorY: '-40'
            }
          }
        ];

        this.setData({
          'circles[0].radius': this.data.scale <= 9.7 ? 0 : this.data.params.radius,
          markers: this.data.scale <= 9.7 ? outMarkes : insideMarkes,
          insideMarkes: insideMarkes,
          outMarkes: outMarkes
        });
        // 这里是请求marck
        // this.setData({
        //   count: res.count,
        //   'markers[0].label.content': num,
        //   'markers[0].label.anchorX': isAn ? anchorX : 0
        // });
      },
      /**
       * 防抖函数 - 防止频繁调用，只在停止触发后执行
       * @param {Function} func 要执行的函数
       * @param {number} delay 延迟时间（毫秒）
       */
      _debounce(func, delay = 1000) {
        // 如果正在更新中，直接返回
        if (this._isUpdating) {
          return;
        }

        // 清除之前的定时器
        if (this._debounceTimer) {
          clearTimeout(this._debounceTimer);
        }

        // 设置新的定时器
        this._debounceTimer = setTimeout(() => {
          console.log('防抖执行，延迟时间:', delay);
          this._isUpdating = true;

          try {
            func();
          } catch (error) {
            console.error('防抖函数执行出错:', error);
          } finally {
            this._isUpdating = false;
            this._debounceTimer = null;
          }
        }, delay);
      },
      //  经纬度 -> 地址名称（逆向解析）- 高德地图SDK
      getLocationName(longitude, latitude, callback) {
        // 参数验证
        if (!longitude || !latitude || typeof callback !== 'function') {
          console.error('getLocationName: 参数无效');
          callback && callback('');
          return;
        }
        // 使用高德地图SDK进行逆向地理编码
        this.myAmapFun.getRegeo({
          location: `${longitude},${latitude}`, // 高德地图格式：经度,纬度
          success: data => {
            // console.log('高德地图SDK返回数据:', data);
            // 获取格式化地址
            let address = '';

            // 高德SDK返回的数据结构可能是数组或对象
            let regeocode = null;
            if (Array.isArray(data) && data.length > 0) {
              regeocode = data[0].regeocodeData;
            } else if (data && data.regeocodeData) {
              regeocode = data.regeocodeData;
            } else if (data && data.regeocode) {
              regeocode = data.regeocode;
            }

            if (regeocode) {
              if (regeocode.formatted_address) {
                address = regeocode.formatted_address;
              } else if (regeocode.addressComponent) {
                // 手动拼接地址
                const addr = regeocode.addressComponent;
                address = `${addr.province || ''}${addr.city || ''}${
                  addr.district || ''
                }${addr.township || ''}`;
              }
            }

            console.log('解析出的地址:', address);
            callback(address || '未知位置');
          },
          fail: error => {
            console.error('高德逆地理编码失败:', error);
            callback('未知位置');
          }
        })
      },

      // 地图区域变化事件处理（拖动地图时触发）
      regionchange(e) {
        const that = this;
        // 拖拽
        if (e.type === 'end' && e.causedBy === 'drag') {
          // console.log('regionchange', e);
          // 中心坐标
          const {
            centerLocation
          } = e.detail;
          this._debounce(() => {
            if (!this.mapCtx) {
              console.error('mapCtx 未初始化');
              return;
            }
            that.getLocationName(
              centerLocation.longitude,
              centerLocation.latitude,
              locationName => {
                that.updateCurrentCenter(
                  centerLocation.latitude,
                  centerLocation.longitude,
                  locationName
                );
              }
            );
          }, 800); // 800ms防抖，拖动停止后执行
        }
        // 放大缩小-真机上中心是不变的 markers要跟着变化
        if (e.type === 'end' && e.causedBy === 'scale') {
          this._debounce(() => {
            const {
              insideMarkes,
              outMarkes
            } = this.data;
            // 拿到缩放比例
            const temScale = Math.ceil(e.detail.scale);
            //小于等于9.7 markers用圈外否者markers用圈内
            this.setData({
              scale: temScale,
              'circles[0].radius': temScale <= 9.7 ? 0 : this.data.params.radius,
              markers: temScale <= 9.7 ? outMarkes : insideMarkes
            });
          }, 800);
        }
      },

      /**
       * 手动设置地图中心
       * @param {number} latitude 纬度
       * @param {number} longitude 经度
       */
      setMapCenter(latitude, longitude) {
        const {
          currentCenter
        } = this.data;
        if (
          latitude == currentCenter.latitude &&
          longitude == currentCenter.longitude
        )
          return;
        if (!this.mapCtx) {
          console.error('mapCtx 未初始化');
          return;
        }

        if (!latitude || !longitude) {
          console.error('setMapCenter: 经纬度参数无效');
          return;
        }

        this.mapCtx.moveToLocation({
          latitude,
          longitude,
          success: () => {
            this.setData({
              latitude,
              longitude
            });
          },
          fail: err => {
            console.error('设置地图中心失败:', err);
          }
        });
      },

      //   设置数据刷新回调函数
      setDataRefreshCallback(callback) {
        if (typeof callback === 'function') {
          this._onDataRefresh = callback;
        } else {
          console.error('setDataRefreshCallback: callback必须是函数');
        }
      },
      // 设置原始中心点（用户当前位置）
      setOriginalCenter(latitude, longitude, locationName = '当前位置') {
        const originalCenter = {
          latitude,
          longitude,
          locationName
        };
        this.setData({
          originalCenter
        });
      },

      /**
       * 回到原始中心点
       */
      backToOriginalCenter() {
        const that = this;
        that.getCurrentLocation((latitude, longitude, name) => {
          that.updateCurrentCenter(latitude, longitude, name);
        });
      },
      /**
       * 初始化圆圈和标记点
       * @param {number} latitude 纬度
       * @param {number} longitude 经度
       */
      initializeCircleAndMarkers(latitude, longitude) {
        // 获取圆圈半径（从页面数据中获取，默认1000米）
        const radius = this.data.params.radius;
        // 初始化圆圈
        const circles = [{
          latitude,
          longitude,
          radius,
          strokeWidth: 2,
          strokeColor: '#FF6B6B',
          fillColor: 'rgba(255, 107, 107, 0.1)'
        }];

        this.setData({
          circles
        });
      },

      /**更新圆圈和*/
      updateCircle(latitude, longitude) {
        this.setData({
          circles: [{
            latitude: +latitude,
            longitude: +longitude,
            color: 'transparent',
            fillColor: 'rgba(231,36,16,0.14)',
            radius: this.data.params.radius,
            strokeWidth: 1.8
          }]
        });
      },
      /**
       * 在圆圈范围内生成随机坐标点
       * @param {number} centerLat 中心纬度
       * @param {number} centerLng 中心经度
       * @param {number} radius 半径（米）
       * @returns {object} 随机坐标点
       */
      generateRandomPointInCircle(centerLat, centerLng, radius) {
        // 地球半径（米）
        const earthRadius = 6371000;

        // 生成随机角度和距离
        const angle = Math.random() * 2 * Math.PI;
        const distance = Math.random() * radius;

        // 将距离转换为弧度
        const distanceRad = distance / earthRadius;

        // 将中心点坐标转换为弧度
        const centerLatRad = (centerLat * Math.PI) / 180;
        const centerLngRad = (centerLng * Math.PI) / 180;

        // 计算新的纬度
        const newLatRad = Math.asin(
          Math.sin(centerLatRad) * Math.cos(distanceRad) +
          Math.cos(centerLatRad) * Math.sin(distanceRad) * Math.cos(angle)
        );

        // 计算新的经度
        const newLngRad =
          centerLngRad +
          Math.atan2(
            Math.sin(angle) * Math.sin(distanceRad) * Math.cos(centerLatRad),
            Math.cos(distanceRad) - Math.sin(centerLatRad) * Math.sin(newLatRad)
          );

        // 转换回度数
        return {
          latitude: (newLatRad * 180) / Math.PI,
          longitude: (newLngRad * 180) / Math.PI
        };
      }
    }
  });
};