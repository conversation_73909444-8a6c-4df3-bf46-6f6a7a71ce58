const app = getApp();
module.exports = function (params) {
  return Behavior({
    data: {
      latitude: 0,
      longitude: 0,
      markers: [],
      locationName: '',
      mapCtx: null,
      error: null // 添加错误信息字段
    },
    lifetimes: {
      attached() {
        // 1. 初始化 mapCtx
        this.mapCtx = wx.createMapContext('map');
        // 2. 启动定位服务
        wx.startLocationUpdate({
          fail: err => {
            console.error('startLocationUpdate 启动失败', err);
            this.setData({
              error: '定位服务启动失败'
            });
          }
        });

        // 3. 监听位置变化
        this._locationChangeHandler = res => {
          // res.latitude, res.longitude
          this.updateMapCenter(
            res.latitude,
            res.longitude,
            (latitude, longitude, markers, locationName) => {
              this.setData({
                latitude,
                longitude,
                markers,
                locationName,
                error: null
              });
            }
          );
        };
        wx.onLocationChange(this._locationChangeHandler);

        // 4. 读取本地缓存（如果有搜索结果）
        const mapSearchItem = wx.getStorageSync('mapSearchItem');
        if (mapSearchItem) {
          const item = JSON.parse(mapSearchItem);
          this.setData(
            {
              locationName: item.title,
              latitude: item.location.lat,
              longitude: item.location.lng
            },
            () => {
              // 5. 缓存命中，直接设置地图中心
              this.setMapCenter(item.location.lat, item.location.lng);
              wx.removeStorageSync('mapSearchItem');
            }
          );
        }
      },
      ready() {
        if (!this.mapCtx) {
          this.mapCtx = wx.createMapContext('map');
        }
      },
      detached() {
        // 停止定位并移除监听
        wx.offLocationChange(this._locationChangeHandler);
        wx.stopLocationUpdate({
          success() {
            console.log('stopLocationUpdate 成功');
          }
        });
      }
    },
    methods: {
      // 经纬度 -> 地址名称
      getLocationName(longitude, latitude, callback) {
        wx.request({
          url: `https://apis.map.qq.com/ws/geocoder/v1/?location=${latitude},${longitude}&key=${this.data.key}`,
          success: res => {
            if (res.data.status === 0) {
              callback(res.data.result.address);
            } else {
              console.error('逆地理编码失败:', res.data.message);
              callback('');
            }
          },
          fail: err => {
            console.error('逆地理编码请求失败:', err);
            callback('');
          }
        });
      },

      // 更新中心：取名字 + markers（如果需要）
      updateMapCenter(latitude, longitude, callback) {
        this.getLocationName(longitude, latitude, locationName => {
          // 如果你不再需要中心点 marker，可传空数组
          const centerMarkers = [];
          callback(latitude, longitude, centerMarkers, locationName);
        });
      },

      // 拖动地图时，仍保留 regionchange 以更新中心地址
      regionchange(e) {
        if (e.type === 'end') {
          // 延迟一下，保证动画结束
          setTimeout(() => {
            this.mapCtx.getCenterLocation({
              success: center => {
                this.updateMapCenter(
                  center.latitude,
                  center.longitude,
                  (latitude, longitude, markers, locationName) => {
                    this.setData({
                      latitude,
                      longitude,
                      markers,
                      locationName,
                      error: null
                    });
                  }
                );
              },
              fail: err => {
                console.error('getCenterLocation 失败:', err);
              }
            });
          }, 100);
        }
      },

      // “回到当前位置”按钮调用：直接 moveToLocation
      backToCurrentLocation() {
        if (this.data.latitude && this.data.longitude) {
          this.mapCtx.moveToLocation();
        }
      },

      // 手动设置地图中心
      setMapCenter(latitude, longitude) {
        this.mapCtx.moveToLocation({
          latitude,
          longitude
        });
        this.setData({
          latitude,
          longitude
        });
      }
    }
  });
};
