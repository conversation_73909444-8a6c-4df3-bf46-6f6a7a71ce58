const app = getApp();

module.exports = function (params) {
  return Behavior({
    data: {
      latitude: 0,
      longitude: 0,
      markers: [],
      locationName: '',
      mapCtx: null,
      error: null,
      // 节流相关
      _throttleTimer: null,
      _lastUpdateTime: 0,
      _isUpdating: false
    },

    lifetimes: {
      attached() {
        this._initializeMap();
        this._initializeLocation();
        this._loadCachedSearchResult();
      },

      ready() {
        if (!this.mapCtx) {
          this.mapCtx = wx.createMapContext('map');
        }
      },

      detached() {
        this._cleanup();
      }
    },

    methods: {
      /**
       * 初始化地图
       */
      _initializeMap() {
        this.mapCtx = wx.createMapContext('map');
      },

      /**
       * 初始化定位服务
       */
      _initializeLocation() {
        wx.startLocationUpdate({
          fail: err => {
            console.error('startLocationUpdate 启动失败', err);
            this.setData({
              error: '定位服务启动失败'
            });
          }
        });

        // 监听位置变化
        this._locationChangeHandler = res => {
          this._handleLocationUpdate(res.latitude, res.longitude);
        };
        wx.onLocationChange(this._locationChangeHandler);
      },

      /**
       * 加载缓存的搜索结果
       */
      _loadCachedSearchResult() {
        try {
          const mapSearchItem = wx.getStorageSync('mapSearchItem');
          if (mapSearchItem) {
            const item = JSON.parse(mapSearchItem);
            this.setData(
              {
                locationName: item.title,
                latitude: item.location.lat,
                longitude: item.location.lng
              },
              () => {
                this.setMapCenter(item.location.lat, item.location.lng);
                wx.removeStorageSync('mapSearchItem');
              }
            );
          }
        } catch (error) {
          console.error('加载缓存搜索结果失败:', error);
          wx.removeStorageSync('mapSearchItem');
        }
      },

      /**
       * 清理资源
       */
      _cleanup() {
        // 清除节流定时器
        if (this._throttleTimer) {
          clearTimeout(this._throttleTimer);
          this._throttleTimer = null;
        }

        // 停止定位并移除监听
        if (this._locationChangeHandler) {
          wx.offLocationChange(this._locationChangeHandler);
        }

        wx.stopLocationUpdate({
          success() {
            console.log('stopLocationUpdate 成功');
          },
          fail(err) {
            console.error('stopLocationUpdate 失败:', err);
          }
        });
      },

      /**
       * 节流函数 - 防止频繁调用
       * @param {Function} func 要执行的函数
       * @param {number} delay 延迟时间（毫秒）
       */
      _throttle(func, delay = 1000) {
        const now = Date.now();

        // 如果正在更新中，直接返回
        if (this._isUpdating) {
          return;
        }

        // 如果距离上次更新时间小于延迟时间，使用定时器延迟执行
        if (now - this._lastUpdateTime < delay) {
          if (this._throttleTimer) {
            clearTimeout(this._throttleTimer);
          }

          this._throttleTimer = setTimeout(() => {
            this._lastUpdateTime = Date.now();
            func();
            this._throttleTimer = null;
          }, delay - (now - this._lastUpdateTime));

          return;
        }

        // 立即执行
        this._lastUpdateTime = now;
        func();
      },

      /**
       * 处理位置更新
       * @param {number} latitude 纬度
       * @param {number} longitude 经度
       */
      _handleLocationUpdate(latitude, longitude) {
        this._throttle(() => {
          this.updateMapCenter(
            latitude,
            longitude,
            (lat, lng, markers, locationName) => {
              this.setData({
                latitude: lat,
                longitude: lng,
                markers,
                locationName,
                error: null
              });
            }
          );
        }, 1500); // 1.5秒节流
      },

      /**
       * 处理地图拖动后的位置更新
       * @param {number} latitude 纬度
       * @param {number} longitude 经度
       */
      _handleMapDragUpdate(latitude, longitude) {
        this._throttle(() => {
          // 调用逆向解析接口
          this.getLocationName(longitude, latitude, locationName => {
            this.setData({
              latitude,
              longitude,
              locationName,
              error: null
            });
          });

          // 预留的接口调用口子 - 每次拖动后调用
          this._onMapDragEnd && this._onMapDragEnd(latitude, longitude);
        }, 800); // 800ms节流，拖动时响应更快
      },

      /**
       * 经纬度 -> 地址名称（逆向解析）- 高德地图API
       * @param {number} longitude 经度
       * @param {number} latitude 纬度
       * @param {Function} callback 回调函数
       */
      getLocationName(longitude, latitude, callback) {
        // 参数验证
        if (!longitude || !latitude || typeof callback !== 'function') {
          console.error('getLocationName: 参数无效');
          callback && callback('');
          return;
        }

        // 使用固定的高德地图API key
        const AMAP_KEY = 'ea478181d97e3d92be0150c05603ddb6';

        wx.request({
          url: `https://restapi.amap.com/v3/geocode/regeo`,
          data: {
            key: AMAP_KEY,
            location: `${longitude},${latitude}`, // 高德地图格式：经度,纬度
            poitype: '',
            radius: 1000,
            extensions: 'base',
            batch: false,
            roadlevel: 0
          },
          timeout: 5000, // 5秒超时
          success: res => {
            if (res.data && res.data.status === '1') {
              // 高德地图成功状态码是字符串'1'
              const regeocode = res.data.regeocode;
              let address = '';

              if (regeocode && regeocode.formatted_address) {
                address = regeocode.formatted_address;
              } else if (regeocode && regeocode.addressComponent) {
                // 如果没有formatted_address，手动拼接地址
                const addr = regeocode.addressComponent;
                address = `${addr.province || ''}${addr.city || ''}${
                  addr.district || ''
                }${addr.township || ''}`;
              }

              callback(address);
            } else {
              console.error(
                '高德逆地理编码失败:',
                res.data?.info || '未知错误'
              );
              callback('');
            }
          },
          fail: err => {
            console.error('高德逆地理编码请求失败:', err);
            callback('');
          }
        });
      },

      /**
       * 更新地图中心点
       * @param {number} latitude 纬度
       * @param {number} longitude 经度
       * @param {Function} callback 回调函数
       */
      updateMapCenter(latitude, longitude, callback) {
        if (typeof callback !== 'function') {
          console.error('updateMapCenter: callback必须是函数');
          return;
        }

        this._isUpdating = true;

        this.getLocationName(longitude, latitude, locationName => {
          // 如果不需要中心点marker，传空数组
          const centerMarkers = [];
          callback(latitude, longitude, centerMarkers, locationName);
          this._isUpdating = false;
        });
      },

      /**
       * 地图区域变化事件处理（拖动地图时触发）
       * @param {Object} e 事件对象
       */
      regionchange(e) {
        if (e.type === 'end') {
          // 延迟获取中心位置，确保动画结束
          setTimeout(() => {
            if (!this.mapCtx) {
              console.error('mapCtx 未初始化');
              return;
            }

            this.mapCtx.getCenterLocation({
              success: center => {
                if (center && center.latitude && center.longitude) {
                  this._handleMapDragUpdate(center.latitude, center.longitude);
                }
              },
              fail: err => {
                console.error('getCenterLocation 失败:', err);
                this.setData({
                  error: '获取地图中心位置失败'
                });
              }
            });
          }, 150); // 稍微增加延迟确保动画完成
        }
      },

      /**
       * 回到当前位置
       */
      backToCurrentLocation() {
        if (!this.mapCtx) {
          console.error('mapCtx 未初始化');
          return;
        }

        if (this.data.latitude && this.data.longitude) {
          this.mapCtx.moveToLocation({
            success: () => {
              console.log('回到当前位置成功');
            },
            fail: err => {
              console.error('回到当前位置失败:', err);
            }
          });
        } else {
          console.warn('当前位置信息不可用');
        }
      },

      /**
       * 手动设置地图中心
       * @param {number} latitude 纬度
       * @param {number} longitude 经度
       */
      setMapCenter(latitude, longitude) {
        if (!this.mapCtx) {
          console.error('mapCtx 未初始化');
          return;
        }

        if (!latitude || !longitude) {
          console.error('setMapCenter: 经纬度参数无效');
          return;
        }

        this.mapCtx.moveToLocation({
          latitude,
          longitude,
          success: () => {
            this.setData({
              latitude,
              longitude
            });
          },
          fail: err => {
            console.error('设置地图中心失败:', err);
          }
        });
      },

      /**
       * 设置地图拖动结束后的回调函数
       * 使用方式：在使用该mixin的页面中调用 this.setMapDragEndCallback(callback)
       * @param {Function} callback 回调函数，参数为 (latitude, longitude)
       */
      setMapDragEndCallback(callback) {
        if (typeof callback === 'function') {
          this._onMapDragEnd = callback;
        } else {
          console.error('setMapDragEndCallback: callback必须是函数');
        }
      }
    }
  });
};
