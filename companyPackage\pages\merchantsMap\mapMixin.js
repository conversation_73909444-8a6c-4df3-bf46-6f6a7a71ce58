const app = getApp();
// 引入高德地图微信小程序SDK
var amapFile = require('../../lib/amap-wx130.js');

module.exports = function (params) {
  return Behavior({
    data: {
      latitude: 0,
      longitude: 0,
      markers: [],
      circles: [],
      locationName: '', // 中心名字
      mapCtx: null,
      error: null,
      // 节流相关
      _throttleTimer: null,
      _lastUpdateTime: 0,
      _isUpdating: false,

      // 中心点管理
      originalCenter: {
        latitude: '',
        longitude: '',
        locationName: '当前位置'
      }, // 原始中心点（用户当前位置）
      currentCenter: {
        latitude: '',
        longitude: '',
        locationName: ''
      }, // 当前中心点（拖动或缩放后的中心点）

      // 业务回调函数
      _onCenterChange: null, // 中心点变化回调
      _onDataRefresh: null // 数据刷新回调
    },

    lifetimes: {
      attached() {
        this._initializeMap();
        this._initializeLocation();
      },

      ready() {
        if (!this.mapCtx) {
          this.mapCtx = wx.createMapContext('map');
        }
      },

      detached() {
        this._cleanup();
      }
    },

    methods: {
      /**
       * 初始化地图
       */
      _initializeMap() {
        this.mapCtx = wx.createMapContext('map');
        // 初始化高德地图SDK
        this.myAmapFun = new amapFile.AMapWX({
          key: 'd9a2bead78cad70c5e01899e3f5d5381'
        });
      },

      /**
       * 初始化定位服务
       */
      _initializeLocation() {
        wx.startLocationUpdate({
          fail: err => {
            console.error('startLocationUpdate 启动失败', err);
            this.setData({
              error: '定位服务启动失败'
            });
          }
        });

        // 监听位置变化
        this._locationChangeHandler = res => {
          console.log('_locationChangeHandler', res);
          this._handleLocationUpdate(res.latitude, res.longitude);
        };
        wx.onLocationChange(this._locationChangeHandler);
      },

      /**
       * 清理资源
       */
      _cleanup() {
        // 清除节流定时器
        if (this._throttleTimer) {
          clearTimeout(this._throttleTimer);
          this._throttleTimer = null;
        }

        // 停止定位并移除监听
        if (this._locationChangeHandler) {
          wx.offLocationChange(this._locationChangeHandler);
        }

        wx.stopLocationUpdate({
          success() {
            console.log('stopLocationUpdate 成功');
          },
          fail(err) {
            console.error('stopLocationUpdate 失败:', err);
          }
        });
      },

      /**
       * 节流函数 - 防止频繁调用
       * @param {Function} func 要执行的函数
       * @param {number} delay 延迟时间（毫秒）
       */
      _throttle(func, delay = 1000) {
        const now = Date.now();

        // 如果正在更新中，直接返回
        if (this._isUpdating) {
          return;
        }

        // 如果距离上次更新时间小于延迟时间，使用定时器延迟执行
        if (now - this._lastUpdateTime < delay) {
          if (this._throttleTimer) {
            clearTimeout(this._throttleTimer);
          }

          this._throttleTimer = setTimeout(() => {
            this._lastUpdateTime = Date.now();
            func();
            this._throttleTimer = null;
          }, delay - (now - this._lastUpdateTime));

          return;
        }

        // 立即执行
        this._lastUpdateTime = now;
        func();
      },

      /**
       * 处理位置更新
       * @param {number} latitude 纬度
       * @param {number} longitude 经度
       */
      _handleLocationUpdate(latitude, longitude) {
        this._throttle(() => {
          // 获取位置名称
          this.getLocationName(longitude, latitude, locationName => {
            this.setData({
              latitude,
              longitude,
              locationName,
              error: null
            });

            // 如果页面有setOriginalCenter方法，设置原始中心点
            if (typeof this.setOriginalCenter === 'function') {
              this.setOriginalCenter(latitude, longitude, locationName);
            }
          });
        }, 1500); // 1.5秒节流
      },

      /**
       * 处理地图拖动后的位置更新
       * @param {number} latitude 纬度
       * @param {number} longitude 经度
       */
      _handleMapDragUpdate(latitude, longitude) {
        this._throttle(() => {
          // 调用逆向解析接口
          this.getLocationName(longitude, latitude, locationName => {
            this.setData({
              latitude,
              longitude,
              locationName,
              error: null
            });
          });

          // 预留的接口调用口子 - 每次拖动后调用
          this._onMapDragEnd && this._onMapDragEnd(latitude, longitude);
        }, 800); // 800ms节流，拖动时响应更快
      },

      /**
       * 经纬度 -> 地址名称（逆向解析）- 高德地图SDK
       * @param {number} longitude 经度
       * @param {number} latitude 纬度
       * @param {Function} callback 回调函数
       */
      getLocationName(longitude, latitude, callback) {
        // 参数验证
        if (!longitude || !latitude || typeof callback !== 'function') {
          console.error('getLocationName: 参数无效');
          callback && callback('');
          return;
        }
        // 使用高德地图SDK进行逆向地理编码
        this.myAmapFun.getRegeo({
          location: `${longitude},${latitude}`, // 高德地图格式：经度,纬度
          success: data => {
            // 获取格式化地址
            let address = '';
            const regeocode = data?.[0]?.regeocodeData
            if (data && regeocode) {
              if (regeocode.formatted_address) {
                address = regeocode.formatted_address;
              } else if (regeocode.addressComponent) {
                // 手动拼接地址
                const addr = regeocode.addressComponent;
                address = `${addr.province || ''}${addr.city || ''}${
                  addr.district || ''
                }${addr.township || ''}`;
              }
            }
            callback(address);
          },
          fail: error => {
            console.error('高德逆地理编码失败:', error);
            callback('');
          }
        });
      },

      /**
       * 地图区域变化事件处理（拖动地图时触发）
       * @param {Object} e 事件对象
       */
      regionchange(e) {
        if (e.type === 'end') {
          // 延迟获取中心位置，确保动画结束
          setTimeout(() => {
            if (!this.mapCtx) {
              console.error('mapCtx 未初始化');
              return;
            }

            this.mapCtx.getCenterLocation({
              success: center => {
                if (center && center.latitude && center.longitude) {
                  this._handleMapDragUpdate(center.latitude, center.longitude);
                  // 更新圆,更新请求
                  
                }
              },
              fail: err => {
                console.error('getCenterLocation 失败:', err);
                this.setData({
                  error: '获取地图中心位置失败'
                });
              }
            });
          }, 150); // 稍微增加延迟确保动画完成
        }
      },

      /**
       * 手动设置地图中心
       * @param {number} latitude 纬度
       * @param {number} longitude 经度
       */
      setMapCenter(latitude, longitude) {
        if (!this.mapCtx) {
          console.error('mapCtx 未初始化');
          return;
        }

        if (!latitude || !longitude) {
          console.error('setMapCenter: 经纬度参数无效');
          return;
        }

        this.mapCtx.moveToLocation({
          latitude,
          longitude,
          success: () => {
            this.setData({
              latitude,
              longitude
            });
          },
          fail: err => {
            console.error('设置地图中心失败:', err);
          }
        });
      },

      /**
       * 设置地图拖动结束后的回调函数
       * 使用方式：在使用该mixin的页面中调用 this.setMapDragEndCallback(callback)
       * @param {Function} callback 回调函数，参数为 (latitude, longitude)
       */
      setMapDragEndCallback(callback) {
        if (typeof callback === 'function') {
          this._onMapDragEnd = callback;
        } else {
          console.error('setMapDragEndCallback: callback必须是函数');
        }
      },

      /**
       * 设置中心点变化回调函数
       * @param {Function} callback 回调函数，参数为 (centerInfo)
       */
      setCenterChangeCallback(callback) {
        if (typeof callback === 'function') {
          this._onCenterChange = callback;
        } else {
          console.error('setCenterChangeCallback: callback必须是函数');
        }
      },

      /**
       * 设置数据刷新回调函数
       * @param {Function} callback 回调函数，参数为 (latitude, longitude)
       */
      setDataRefreshCallback(callback) {
        if (typeof callback === 'function') {
          this._onDataRefresh = callback;
        } else {
          console.error('setDataRefreshCallback: callback必须是函数');
        }
      },

      /**
       * 更新当前中心点（拖动或缩放后）
       * @param {number} latitude 纬度
       * @param {number} longitude 经度
       */
      updateCurrentCenter(latitude, longitude) {
        // 获取当前中心点的地址名称
        this.getLocationName(longitude, latitude, locationName => {
          const currentCenter = {
            latitude,
            longitude,
            locationName: locationName || '未知位置'
          };

          // 更新当前中心点和地图数据
          this.setData({
            latitude,
            longitude,
            currentCenter,
            locationName: currentCenter.locationName
          });

          // 更新圆圈和标记点位置
          this.updateCircleAndMarkers(latitude, longitude);

          // 生成mock数据
          this.generateMockMarkers(latitude, longitude);

          // 触发中心点变化回调
          if (this._onCenterChange) {
            this._onCenterChange(currentCenter);
          }

          // 触发数据刷新回调
          if (this._onDataRefresh) {
            this._onDataRefresh(latitude, longitude);
          }
        });
      },

      /**
       * 设置原始中心点（用户当前位置）
       * @param {number} latitude 纬度
       * @param {number} longitude 经度
       * @param {string} locationName 位置名称
       */
      setOriginalCenter(latitude, longitude, locationName = '当前位置') {
        const originalCenter = {
          latitude,
          longitude,
          locationName
        };

        this.setData({
          originalCenter,
          currentCenter: {
            ...originalCenter
          },
          locationName: locationName
        });

        // 初始化圆圈和标记点
        this.initializeCircleAndMarkers(latitude, longitude);

        // 生成初始mock数据
        this.generateMockMarkers(latitude, longitude);
      },

      /**
       * 回到原始中心点
       */
      backToOriginalCenter() {
        const {
          originalCenter
        } = this.data;
        if (originalCenter.latitude && originalCenter.longitude) {
          // 移动地图到原始位置
          this.setMapCenter(originalCenter.latitude, originalCenter.longitude);

          // 更新当前中心点为原始中心点
          this.setData({
            latitude: originalCenter.latitude,
            longitude: originalCenter.longitude,
            currentCenter: {
              ...originalCenter
            },
            locationName: originalCenter.locationName
          });

          // 更新圆圈和标记点
          this.updateCircleAndMarkers(
            originalCenter.latitude,
            originalCenter.longitude
          );

          // 生成mock数据
          this.generateMockMarkers(
            originalCenter.latitude,
            originalCenter.longitude
          );

          // 触发中心点变化回调
          if (this._onCenterChange) {
            this._onCenterChange(originalCenter);
          }

          // 触发数据刷新回调
          if (this._onDataRefresh) {
            this._onDataRefresh(
              originalCenter.latitude,
              originalCenter.longitude
            );
          }
        }
      },

      /**
       * 地图缩放事件处理
       */
      onMapScale() {
        // 获取当前地图中心点
        setTimeout(() => {
          if (!this.mapCtx) {
            console.error('mapCtx 未初始化');
            return;
          }

          this.mapCtx.getCenterLocation({
            success: center => {
              if (center && center.latitude && center.longitude) {
                this.updateCurrentCenter(center.latitude, center.longitude);
              }
            },
            fail: err => {
              console.error('获取地图中心位置失败:', err);
              this.setData({
                error: '获取地图中心位置失败'
              });
            }
          });
        }, 300); // 延迟确保缩放动画完成
      },

      /**
       * 初始化圆圈和标记点
       * @param {number} latitude 纬度
       * @param {number} longitude 经度
       */
      initializeCircleAndMarkers(latitude, longitude) {
        // 获取圆圈半径（从页面数据中获取，默认1000米）
        const radius = (this.data.sliderVal || 2) * 1000;

        // 初始化圆圈
        const circles = [{
          latitude,
          longitude,
          radius,
          strokeWidth: 2,
          strokeColor: '#FF6B6B',
          fillColor: 'rgba(255, 107, 107, 0.1)'
        }];

        this.setData({
          circles
        });
      },

      /**
       * 更新圆圈和标记点位置
       * @param {number} latitude 纬度
       * @param {number} longitude 经度
       */
      updateCircleAndMarkers(latitude, longitude) {
        // 更新圆圈位置
        if (this.data.circles && this.data.circles.length > 0) {
          this.setData({
            'circles[0].latitude': latitude,
            'circles[0].longitude': longitude
          });
        }
      },

      /**
       * 生成mock标记点数据（在圆圈范围内）
       * @param {number} centerLat 中心纬度
       * @param {number} centerLng 中心经度
       */
      generateMockMarkers(centerLat, centerLng) {
        const radius = (this.data.sliderVal || 2) * 1000; // 圆圈半径（米）
        const mockMarkers = [];

        // 生成10-20个随机标记点
        const markerCount = Math.floor(Math.random() * 11) + 10; // 10-20个

        for (let i = 0; i < markerCount; i++) {
          // 在圆圈范围内生成随机坐标
          const randomPoint = this.generateRandomPointInCircle(
            centerLat,
            centerLng,
            radius
          );

          mockMarkers.push({
            id: i + 2, // 从2开始，因为中心点marker的id是1
            latitude: randomPoint.latitude,
            longitude: randomPoint.longitude,
            iconPath: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/company_marker.png',
            width: 25,
            height: 25,
            label: {
              content: `企业${i + 1}`,
              color: '#333',
              fontSize: 12,
              anchorY: -5,
              textAlign: 'center'
            }
          });
        }

        // 合并中心点marker和mock markers
        const allMarkers = [{
            id: 1,
            latitude: centerLat,
            longitude: centerLng,
            iconPath: 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/marker.png',
            width: 30,
            height: 30,
            label: {
              content: this.data.count > 99 ?
                '99+' : (this.data.count || 0).toString(),
              color: '#fff',
              anchorY: -26,
              textAlign: 'center'
            }
          },
          ...mockMarkers
        ];

        this.setData({
          markers: allMarkers
        });
      },

      /**
       * 在圆圈范围内生成随机坐标点
       * @param {number} centerLat 中心纬度
       * @param {number} centerLng 中心经度
       * @param {number} radius 半径（米）
       * @returns {object} 随机坐标点
       */
      generateRandomPointInCircle(centerLat, centerLng, radius) {
        // 地球半径（米）
        const earthRadius = 6371000;

        // 生成随机角度和距离
        const angle = Math.random() * 2 * Math.PI;
        const distance = Math.random() * radius;

        // 将距离转换为弧度
        const distanceRad = distance / earthRadius;

        // 将中心点坐标转换为弧度
        const centerLatRad = (centerLat * Math.PI) / 180;
        const centerLngRad = (centerLng * Math.PI) / 180;

        // 计算新的纬度
        const newLatRad = Math.asin(
          Math.sin(centerLatRad) * Math.cos(distanceRad) +
          Math.cos(centerLatRad) * Math.sin(distanceRad) * Math.cos(angle)
        );

        // 计算新的经度
        const newLngRad =
          centerLngRad +
          Math.atan2(
            Math.sin(angle) * Math.sin(distanceRad) * Math.cos(centerLatRad),
            Math.cos(distanceRad) - Math.sin(centerLatRad) * Math.sin(newLatRad)
          );

        // 转换回度数
        return {
          latitude: (newLatRad * 180) / Math.PI,
          longitude: (newLngRad * 180) / Math.PI
        };
      },

      /**
       * 更新圆圈半径
       * @param {number} sliderVal 滑块值
       */
      updateCircleRadius(sliderVal) {
        const radius = sliderVal * 1000;

        if (this.data.circles && this.data.circles.length > 0) {
          this.setData({
            'circles[0].radius': radius,
            sliderVal
          });

          // 重新生成mock数据
          if (
            this.data.currentCenter.latitude &&
            this.data.currentCenter.longitude
          ) {
            this.generateMockMarkers(
              this.data.currentCenter.latitude,
              this.data.currentCenter.longitude
            );
          }
        }
      }
    }
  });
};