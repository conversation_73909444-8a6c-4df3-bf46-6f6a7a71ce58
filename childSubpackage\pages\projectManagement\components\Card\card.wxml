<!--childSubpackage/pages/projectManagement/components/Card/card.wxml-->
<view class="card">
  <view class="info">
    <!-- 复选框 -->
    <view class="checkbox {{projectItem.checked && 'checked'}}" bindtap="handleCheck"></view>
    <image class="logo" src="{{projectItem.logo}}" mode=""/>
    <view class="detail">
      <view class="title">
        <text>{{projectItem.ent_name}}</text>
        <text class="icon-del" wx:if="{{!trackUserId}}" bindtap="delProject"></text>
      </view>
      <view class="tags">
        <text wx:if="{{projectItem.project_type_text}}" class="tag type">{{projectItem.project_type_text}}</text>
        <text wx:if="{{projectItem.project_process_text}}" class="tag rate">{{projectItem.project_process_text}}</text>
      </view>
      <view class="time">收藏时间 {{projectItem.collect_date}}</view>
    </view>
  </view>
  <!-- 操作按钮 -->
  <view class="operate">
    <view 
      class="btn" 
      wx:for="{{operateBtnArr}}" 
      wx:key="name"
      data-item="{{projectItem}}"
      data-type="{{item.type}}"
      bindtap="cardFun"
    >
      <image class="icon" src="{{item.icon}}" mode=""/>
      <text>{{item.name}}</text>
    </view>
  </view>
</view>