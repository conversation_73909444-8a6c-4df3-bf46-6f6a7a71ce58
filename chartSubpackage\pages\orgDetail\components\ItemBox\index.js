// automobile/pages/infrastructure/ItemBox/index.js
import {moreListType} from "../../data";

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    legend: Array,
    data: {
      type: Array,
      value: [],
      observer(val){
        if(val.length) {
          let sum =  val.map((item) => Number(item.value)).reduce((prev, cur) => {
            return prev + cur;
          }, 0);
          const dataList = val.sort((a, b) => b.value - a.value);
          this.setData({ maxNum: sum, dataList })
        }
      },
    },
  },

  /**
   * 组件的初始数据
   */
  data: {
    maxNum: 1,
    dataList: []
  },

  /**
   * 组件的方法列表
   */
  methods: {
  }
})
