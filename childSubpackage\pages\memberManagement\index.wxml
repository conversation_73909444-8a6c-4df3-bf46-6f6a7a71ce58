<import src="/template/null/null"></import>

<view class="member_wrapper">
  <view class="header">
    <view class="header_item"> 
      <view class="name"> {{ accountDetail.admin_user }} <text>管理员</text></view>
      <view class="name_right" bindtap="addUser"><text class="add"></text> 邀请成员</view>
    </view>
    <view class="header_item"> 
      <view class="account"> 子账号数量: <text>{{accountDetail.current}}/{{accountDetail.limit}}</text></view>
      <view class="account_right" bindtap="checkUser"><text class="check"></text>成员审核</view>
    </view>
  </view>
  <view class="sub_account">
    <view class="sub_account_title">子账号 </view>
    <scroll-view scroll-y  style="height: {{cardHeight}}px;">
      <block wx:for="{{ subAccount }}" wx:key="index">
        <AccountCard binddelete="handleDelete" item="{{ item }}" />
      </block>
      <!--暂无数据 bazaarIsNull-->
      <view wx:if="{{!subAccount.length}}" style="width: 100%;height: {{cardHeight}}px;">
        <template is='null'></template>
      </view>
    </scroll-view>
  </view>
  
  <!-- 删除成员 -->
  <Dialog title="删除成员" cancelBtnText="交接并删除" confirmBtnText="直接删除" visible="{{deleteVisible}}" showCloseBtn="{{ false }}"  bindclose="deleteClose" bindsubmit='deleteSubmit'>
    <view class="delete_dialog_content">
      <view class="title"><view  class="icon"> </view> 是否需要强制交接该成员项目  </view>
      <view class="info">  仅支持交接管理员分配项目  </view>
    </view>
  </Dialog>

  <!-- 邀请成员 -->
  <Dialog title="邀请成员" cancelBtnText="取消" confirmBtnText="立即分享"  showCloseBtn="{{ true }}" visible="{{addVisible}}" bindclose="addClose" bindsubmit='addSubmit'>
    <view class="invite">
      <block wx:for="{{textArr}}" wx:key="index" data-item="item">
        <view class="list">
          <view class="title">
            <text> {{ item }}</text>
          </view>
        </view>
      </block>
    </view>
  </Dialog>
</view>