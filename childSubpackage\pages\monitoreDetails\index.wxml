<import src="/template/tabBar/index"></import>
<import src="/template/null/null"></import>
<import src="/template/more/more"></import>
<import src="./common/lFilter/list"></import>
<import src="./common/lFilter/rlist"></import>
<import src="./common/lFilter/pop"></import>

<view class="home-wrap">
  <CustomNavbar text="监控详情" id="navigationBar" class="navigationBar" textPositon="center" navColor="{{['rgba(255, 255, 255, 1)', 'rgba(255, 255, 255, 1)']}}" showNav navBg="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/other/bg0.png" navBg0="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/other/statusbg.png" />
  <scroll-view class="home" style="height: {{contentH}}px" scroll-y bindscroll="onScroll" throttle="{{true}}" bindscrolltolower="loadMores" scroll-top="{{scrollTop}}" refresher-triggered="{{fresher}}" refresher-background="#ee6b5e" refresher-default-style="white" refresher-threshold="{{25}}" bindrefresherrefresh="onfresherss" refresher-enabled will-change='transform'>
    <view class="h_head">
      <!-- 图片带补充 -->
      <image class="head_bg" src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/other/bg1.png" mode="aspectFill"></image>
      <!-- 头部 -->
      <view class="head-top">
        <view class="card-boxs">
          <view class="card-logo">
            <image src="{{!objData.logo ? 'https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/default.png':objData.logo }}" binderror="errorFunction" class="img" data-index="{{index}}"></image>
          </view>
          <view class="card-title">
            {{objData.ent_name || '--'}}
          </view>
        </view>
        <view class="card_c">
          <view class="card_c_i ">
            <text class="name">法人代表人</text>
            <text class="cont">{{objData.legal_person || '--'}}</text>
          </view>
          <view class="card_c_i card_c_is  zj">
            <text class="name">注册资本</text>
            <text class="cont">{{objData.register_capital || '--' }}万</text>
          </view>
          <view class="card_c_i">
            <text class="name">成立日期</text>
            <text class="cont">{{objData.register_date || '--' }}</text>
          </view>
        </view>
        <view class="card_ico">
          <view class="card_ico_i" catchtap="official" data-item="{{objData}}" wx:if="{{objData.official_website}}">
            <view class="card_ico_i_img">
              <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"></image>
            </view>
            <view>
              官网
            </view>
          </view>
          <view class="card_ico_i" catchtap="site" data-item="{{objData}}" wx:if="{{objData.location.lon!=''&&objData.location.lat!=''}}">
            <view class="card_ico_i_img">
              <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"></image>
            </view>
            <view>
              地址
            </view>
          </view>
          <view class="card_ico_i" catchtap="baogao" data-item="{{objData}}">
            <view class="card_ico_i_img">
              <image src="https://hd-custom.oss-cn-beijing.aliyuncs.com/cdn/images/app/imgs/image/home/<USER>"></image>
            </view>
            <view>
              企业报告
            </view>
          </view>
        </view>
      </view>
      <!-- 导航+内容 -->
      <view id="navbars" style="height: 192rpx;">
        <template is="nav" data="{{activeIndex,tabs,sliderLeft,sliderOffset,sliderWidth}}"></template>
        <!-- 筛选框孩纸 -->
        <view wx:if="{{activeIndex==0}}" bindtap='goTop'>
          <template is='lfiter' data="{{dropDownMenuTitle:polishingDropDownMenuTitle,district_open:polishingOne,source_open:polishingTwo,district_val:polishingOne_val,source_val:polishingTwo_val ,filter_open:polishingThree,filter_val:polishingThree_val}}"></template>
        </view>
        <view wx:if="{{activeIndex==1}}" bindtap='goTop'>
          <template is='rfiter' data="{{dropDownMenuTitle:yuqinDropDownMenuTitle,district_open:yuqinOne,source_open:yuqinTwo,district_val:yuqinOne_val,source_val:yuqinTwo_val }}"></template>
        </view>
      </view>
      <!-- 内容 -->
      <view class="content" style="min-height: calc(100vh - {{contentTop}}rpx)">
        <!-- 内容0 -->
        <view class="optimization " wx:if="{{activeIndex == 0}}" style="min-height:calc(100vh - {{popTopH}}px);">
          <view wx:if="{{!lbxqIsNull}}">
            <CardList entList="{{lbxqList}}" />
            <view wx:if="{{lbxqList.length<lbxqCount}}" style="width: 100%;">
              <template is='more' data="{{hasData:lbxqHasData}}"></template>
            </view>
          </view>
          <view style="width: 100%;height:calc(100vh - {{popTopH}}px);" wx:if="{{lbxqIsNull}}">
            <template is='null'></template>
          </view>
        </view>
        <!-- 内容1 -->
        <view class="optimization " wx:if="{{activeIndex == 1}}" style="min-height:calc(100vh - {{popTopH}}px);">
          <view wx:if="{{!yuqinIsNull}}">
            <yuqingList entList="{{yuqinList}}" />
            <view wx:if="{{yuqinList.length<yuqinCount}}" style="width: 100%;">
              <template is='more' data="{{hasData:yuqinHasData}}"></template>
            </view>
          </view>
          <view style="width: 100%;height:calc(100vh - {{popTopH}}px);" wx:if="{{yuqinIsNull}}">
            <template is='null'></template>
          </view>
        </view>
      </view>
    </view>
  </scroll-view>

  <!--   fiexed -->
  <view class=" home_tesu {{isFixed?'home_tesu_on':'home_tesu_off'}}" style="top: {{navigationBarH}}px; height: 192rpx;">
    <template is="nav" data="{{activeIndex,tabs,sliderLeft,sliderOffset,sliderWidth}}"></template>
    <view wx:if="{{activeIndex==0}}" bindtap='goTop'>
      <template is='lfiter' data="{{dropDownMenuTitle:polishingDropDownMenuTitle,district_open:polishingOne,source_open:polishingTwo,district_val:polishingOne_val,source_val:polishingTwo_val,filter_open:polishingThree,filter_val:polishingThree_val}}"></template>
    </view>
    <view wx:if="{{activeIndex==1}}" bindtap='goTop'>
      <template is='rfiter' data="{{dropDownMenuTitle:yuqinDropDownMenuTitle,district_open:yuqinOne,source_open:yuqinTwo,district_val:yuqinOne_val,source_val:yuqinTwo_val }}"></template>
    </view>
  </view>



  <!-- 筛选框--风险级别 -->
  <template is='pop' data="{{popResH:popResH,popTopH:popTopH,isShow:polishingOne, sourceData:polishingOneData, ChooseMethod:'polishingChoose', dataSourceName:'polishingOneData', dataShows:'polishingOne_val'}}"></template>

  <!-- 筛选框--风险类别 -->
  <template is='pop' data="{{popResH:popResH,popTopH:popTopH,isShow:polishingTwo, sourceData:polishingTwoData, ChooseMethod:'polishingChoose', dataSourceName:'polishingTwoData', dataShows:'polishingTwo_val'}}"></template>

  <!-- 筛选框--统计时间 -->
  <template is='pop' data="{{popResH:popResH,popTopH:popTopH,isShow:polishingThree, sourceData:polishingThreeData, ChooseMethod:'polishingChoose', dataSourceName:'polishingThreeData', dataShows:'polishingThree_val'}}"></template>

  <!-- 舆情倾向 -->
  <template is='pop' data="{{popResH:popResH,popTopH:popTopH,isShow:yuqinOne, sourceData:yuqinOneData, ChooseMethod:'yuqinChoose', dataSourceName:'yuqinOneData', dataShows:'yuqinOne_val'}}"></template>
  <!-- 舆情类别 -->
  <template is='pop' data="{{popResH:popResH,popTopH:popTopH,isShow:yuqinTwo, sourceData:yuqinTwoData, ChooseMethod:'yuqinChoose', dataSourceName:'yuqinTwoData', dataShows:'yuqinTwo_val'}}"></template>

  <dialog visible='{{showAddress}}' title="地址" isShowConfirm='{{false}}' showFooter="{{false}}">
    <view class="dialog-con">
      <view style="padding: 0 48rpx;">
        <map id="map" longitude="{{location.lon}}" latitude="{{location.lat}}" markers="{{addmarkers}}" scale="15" style="width: 100%; height: 306rpx;">
        </map>
      </view>
      <view style="margin: 32rpx 0;font-size: 28rpx;">{{locationTxt}}</view>
      <view bindtap="goMap" class="map">
        导航
      </view>
      <view class="cancel" bindtap="onCloseAddress">
        取消
      </view>
    </view>
  </dialog>

</view>